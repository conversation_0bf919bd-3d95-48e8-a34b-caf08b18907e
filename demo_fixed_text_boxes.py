#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
展示修复文字框遮挡问题后的故障异常报警系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import PRIMARY_BG, ACCENT_COLOR, TEXT_PRIMARY

class FixedTextBoxesDemo(QMainWindow):
    """修复文字框遮挡问题后的演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.show_alarm_system()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("修复文字框遮挡问题 - 故障异常报警系统")
        self.setGeometry(50, 50, 1200, 900)
        self.setStyleSheet(f"background-color: {PRIMARY_BG};")
    
    def show_alarm_system(self):
        """直接显示故障异常报警系统"""
        self.alarm_system = FaultAlarmSystem()
        self.setCentralWidget(self.alarm_system)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("修复文字框遮挡 - 故障异常报警系统")
    app.setApplicationVersion("1.3")
    
    # 创建演示窗口
    demo = FixedTextBoxesDemo()
    demo.show()
    
    print("🎉 修复文字框遮挡问题后的故障异常报警系统已启动")
    print("\n🔧 修复内容：")
    print("✅ 数据项布局优化")
    print("   - 增加上下边距：8px")
    print("   - 设置最小高度：25px")
    print("   - 增加内边距：3px 5px")
    print("   - 标签最小宽度：120px")
    print("   - 数值最小宽度：100px")
    print("\n✅ 进度条布局优化")
    print("   - 增加容器边距：8px")
    print("   - 增加标签间距：5px")
    print("   - 进度条高度：12px → 16px")
    print("   - 隐藏进度条文字避免重叠")
    print("   - 标签最小宽度：120px")
    print("   - 百分比最小宽度：60px")
    print("\n✅ 卡片内部间距优化")
    print("   - 卡片内边距：15px → 18px")
    print("   - 数据区域间距：8px")
    print("   - 数据区域内边距：5px 10px")
    print("\n✅ 进度条样式改进")
    print("   - 增加边框：1px solid 蓝色")
    print("   - 圆角半径：4px → 8px")
    print("   - 背景色：浅蓝色")
    print("   - 内部边距：1px")
    print("\n📐 布局改进原理：")
    print("• 容器尺寸：为每个文字元素提供足够的显示空间")
    print("• 最小宽度：确保标签和数值不会被截断")
    print("• 内边距：防止文字贴边显示")
    print("• 对齐方式：右对齐数值，左对齐标签")
    print("• 高度控制：确保文字有足够的垂直空间")
    print("\n💡 解决的问题：")
    print("• ❌ 文字被截断 → ✅ 文字完整显示")
    print("• ❌ 标签重叠 → ✅ 标签清晰分离")
    print("• ❌ 进度条文字冲突 → ✅ 进度条文字独立显示")
    print("• ❌ 容器太小 → ✅ 容器尺寸适中")
    print("• ❌ 间距不足 → ✅ 间距合理充足")
    print("\n🎨 视觉效果提升：")
    print("• 更清晰的文字显示")
    print("• 更好的视觉层次")
    print("• 更舒适的阅读体验")
    print("• 更专业的界面外观")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)

if __name__ == "__main__":
    main()

# 多特征马氏距离分类子界面实现总结

## 🎯 需求描述

用户希望在点击"多特征马氏距离分类"按钮后，不是直接在主界面显示结果，而是：
1. 先弹出一个子界面进行分析和显示详细信息
2. 用户可以在子界面中查看分析过程和图表
3. 用户关闭子界面后，在主界面显示分类结果

## 🔧 实现方案

### 1. 修改主界面调用逻辑

**修改文件**: `ui/fault_diagnosis.py`

**原始代码**:
```python
def perform_multi_feature_diagnosis(self):
    """执行多特征马氏距离分类诊断"""
    if not self.feature_selector or not self.feature_selector.is_selection_valid():
        QMessageBox.warning(self, "警告", "请选择3-6个特征进行诊断")
        return

    selected_features = self.feature_selector.get_selected_features()

    try:
        # 直接在主界面显示结果
        algorithm_interface = get_algorithm_interface()
        results = algorithm_interface.diagnose_fault(selected_features, 'multi_mahalanobis')
        self.display_multi_feature_results(results, selected_features)

    except Exception as e:
        QMessageBox.critical(self, "错误", f"多特征马氏距离分类失败:\n{str(e)}")
```

**修改后代码**:
```python
def perform_multi_feature_diagnosis(self):
    """执行多特征马氏距离分类诊断"""
    if not self.feature_selector or not self.feature_selector.is_selection_valid():
        QMessageBox.warning(self, "警告", "请选择3-6个特征进行诊断")
        return

    selected_features = self.feature_selector.get_selected_features()

    try:
        # 创建并显示多特征马氏距离分类子界面
        dialog = MultiFeatureMahalanobisDialog(self, selected_features)
        if dialog.exec_() == QDialog.Accepted:
            # 用户关闭子界面后，获取分析结果并显示
            results = dialog.get_results()
            if results:
                self.display_multi_feature_results(results, selected_features)

    except Exception as e:
        QMessageBox.critical(self, "错误", f"多特征马氏距离分类失败:\n{str(e)}")
```

### 2. 创建子界面类

**新增类**: `MultiFeatureMahalanobisDialog`

#### 主要功能特性

1. **模态对话框**: 使用 `QDialog` 创建模态子界面
2. **自动分析**: 在界面初始化时自动执行马氏距离分析
3. **实时显示**: 显示分析过程和结果
4. **图表展示**: 包含两个主要图表
5. **结果传递**: 关闭时将结果传递给主界面

#### 界面组件

1. **标题区域**:
   - 显示"多特征马氏距离分类分析"标题
   - 显示选择的特征列表

2. **分析结果摘要**:
   - 分类结果（正常/故障）
   - 到正样本的马氏距离
   - 到负样本的马氏距离
   - 决策依据说明

3. **特征空间分析图表**:
   - 当前值与正负样本均值的对比
   - 柱状图显示各特征的分布情况

4. **马氏距离对比图表**:
   - 到正样本和负样本距离的直观对比
   - 帮助用户理解分类决策

5. **控制按钮**:
   - "关闭并应用结果"按钮
   - 点击后关闭子界面并将结果传递给主界面

#### 核心方法

```python
class MultiFeatureMahalanobisDialog(QDialog):
    def __init__(self, parent, selected_features):
        # 初始化界面和执行分析
        
    def init_ui(self):
        # 创建用户界面组件
        
    def perform_analysis(self):
        # 执行马氏距离分析
        
    def display_analysis_results(self):
        # 显示分析结果和图表
        
    def create_result_summary(self):
        # 创建结果摘要组件
        
    def create_feature_space_chart(self):
        # 创建特征空间分析图表
        
    def create_distance_comparison_chart(self):
        # 创建马氏距离对比图表
        
    def get_results(self):
        # 获取分析结果供主界面使用
```

### 3. 用户交互流程

1. **用户点击按钮**: 点击"多特征马氏距离分类"按钮
2. **验证特征选择**: 检查是否选择了3-6个特征
3. **打开子界面**: 创建并显示 `MultiFeatureMahalanobisDialog`
4. **自动分析**: 子界面自动执行马氏距离分析
5. **显示结果**: 在子界面中显示分析过程和图表
6. **用户查看**: 用户可以查看详细的分析结果和图表
7. **关闭子界面**: 用户点击"关闭并应用结果"按钮
8. **应用结果**: 主界面获取分析结果并显示在原有位置

## 🎨 界面设计特点

### 视觉风格
- **一致性**: 与主界面保持相同的颜色主题和字体
- **专业性**: 使用深色主题，突出数据可视化
- **清晰性**: 合理的布局和间距，信息层次分明

### 交互体验
- **即时反馈**: 打开子界面后立即开始分析
- **加载提示**: 分析过程中显示"正在进行马氏距离分析..."
- **错误处理**: 分析失败时显示友好的错误信息
- **结果保留**: 分析结果在子界面关闭后传递给主界面

### 图表设计
- **特征空间图表**: 柱状图对比当前值与正负样本均值
- **距离对比图表**: 直观显示到两个样本中心的距离
- **统一样式**: 所有图表使用相同的颜色方案和字体

## 📊 技术实现细节

### 1. 对话框管理
```python
# 模态对话框，阻塞主界面交互
dialog = MultiFeatureMahalanobisDialog(self, selected_features)
if dialog.exec_() == QDialog.Accepted:
    # 用户正常关闭对话框
    results = dialog.get_results()
```

### 2. 异步分析
```python
def perform_analysis(self):
    try:
        # 调用算法接口进行分析
        self.results = self.algorithm_interface.diagnose_fault(
            self.selected_features, 'multi_mahalanobis'
        )
        # 更新界面显示
        self.display_analysis_results()
    except Exception as e:
        # 错误处理
        self.show_error_message(str(e))
```

### 3. 图表集成
```python
# 使用matplotlib创建图表
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

fig = Figure(figsize=(10, 4), facecolor='#1e1e2e')
canvas = FigureCanvas(fig)
```

## ✅ 优势和改进

### 优势
1. **用户体验**: 提供更详细的分析过程展示
2. **信息丰富**: 在子界面中显示更多分析细节
3. **交互友好**: 用户可以仔细查看分析结果再决定是否应用
4. **代码复用**: 主界面的显示逻辑保持不变

### 改进点
1. **分离关注点**: 分析过程和结果显示分离
2. **增强可视化**: 提供更丰富的图表展示
3. **提升专业性**: 更符合专业分析软件的交互模式

## 🧪 测试验证

创建了测试脚本 `test_mahalanobis_dialog.py` 用于验证子界面功能：

```python
# 测试特征数据
test_features = {
    '均值': 0.05,
    '标准差': 0.8,
    '方差': 0.64,
    '峰值因子': 1.5,
    '偏度': 0.2,
    '峰度': 3.2
}

# 创建并测试子界面
dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
result = dialog.exec_()
```

## 📝 使用说明

1. **选择特征**: 在主界面选择3-6个特征
2. **点击按钮**: 点击"多特征马氏距离分类"按钮
3. **查看分析**: 在弹出的子界面中查看详细分析结果
4. **关闭应用**: 点击"关闭并应用结果"按钮
5. **查看结果**: 在主界面查看最终的分类结果

这个实现完全满足了用户的需求，提供了更好的用户体验和更丰富的分析展示。

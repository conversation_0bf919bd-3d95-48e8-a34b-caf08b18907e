"""
训练配置对话框
用于配置深度学习模型的网络架构和训练参数
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox, QFormLayout,
    QDialogButtonBox, QTabWidget, QWidget, QTextEdit, QScrollArea
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)


class TrainingConfigDialog(QDialog):
    """训练配置对话框"""
    
    def __init__(self, parent=None, current_config=None):
        super().__init__(parent)
        self.current_config = current_config or {}
        self.init_ui()
        self.load_current_config()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("训练参数配置")
        self.setModal(True)
        self.resize(600, 700)
        
        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
            }}
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {SECONDARY_BG};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {ACCENT_COLOR};
            }}
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 14px;
            }}
            QComboBox, QSpinBox, QDoubleSpinBox {{
                padding: 8px;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 6px;
                background-color: {SECONDARY_BG};
                color: {TEXT_PRIMARY};
                font-size: 14px;
                min-height: 20px;
            }}
            QCheckBox {{
                color: {TEXT_PRIMARY};
                font-size: 14px;
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid {ACCENT_COLOR};
                background-color: {SECONDARY_BG};
                border-radius: 3px;
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid {SUCCESS_COLOR};
                background-color: {SUCCESS_COLOR};
                border-radius: 3px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("深度学习训练参数配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 网络架构标签页
        self.create_architecture_tab()
        
        # 训练参数标签页
        self.create_training_params_tab()
        
        # 数据处理标签页
        self.create_data_processing_tab()
        
        # 按钮区域
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {SUCCESS_COLOR};
            }}
        """)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def create_architecture_tab(self):
        """创建网络架构配置标签页"""
        arch_widget = QWidget()
        layout = QVBoxLayout(arch_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # 预定义架构选择
        predefined_group = QGroupBox("预定义网络架构")
        predefined_layout = QFormLayout(predefined_group)
        
        self.architecture_combo = QComboBox()
        self.architecture_combo.addItems([
            "简单MLP", "深层MLP", "宽层MLP", "自定义"
        ])
        self.architecture_combo.currentTextChanged.connect(self.on_architecture_changed)
        predefined_layout.addRow("架构类型:", self.architecture_combo)
        
        layout.addWidget(predefined_group)
        
        # 自定义架构配置
        custom_group = QGroupBox("自定义网络架构")
        custom_layout = QFormLayout(custom_group)
        
        self.hidden_layers_edit = QTextEdit()
        self.hidden_layers_edit.setMaximumHeight(100)
        self.hidden_layers_edit.setPlaceholderText("输入隐藏层神经元数量，用逗号分隔\n例如: 128, 64, 32")
        self.hidden_layers_edit.textChanged.connect(self.update_architecture_preview)
        custom_layout.addRow("隐藏层配置:", self.hidden_layers_edit)

        self.dropout_spin = QDoubleSpinBox()
        self.dropout_spin.setRange(0.0, 0.9)
        self.dropout_spin.setSingleStep(0.1)
        self.dropout_spin.setValue(0.3)
        self.dropout_spin.setDecimals(2)
        self.dropout_spin.valueChanged.connect(self.update_architecture_preview)
        custom_layout.addRow("Dropout率:", self.dropout_spin)
        
        layout.addWidget(custom_group)
        
        # 架构预览
        preview_group = QGroupBox("架构预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.architecture_preview = QTextEdit()
        self.architecture_preview.setReadOnly(True)
        self.architecture_preview.setMaximumHeight(150)
        preview_layout.addWidget(self.architecture_preview)
        
        layout.addWidget(preview_group)
        
        layout.addStretch()
        self.tab_widget.addTab(arch_widget, "网络架构")
        
    def create_training_params_tab(self):
        """创建训练参数配置标签页"""
        params_widget = QWidget()
        layout = QVBoxLayout(params_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # 基本训练参数
        basic_group = QGroupBox("基本训练参数")
        basic_layout = QFormLayout(basic_group)
        
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(10, 1000)
        self.epochs_spin.setValue(100)
        basic_layout.addRow("训练轮数 (Epochs):", self.epochs_spin)
        
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(8, 512)
        self.batch_size_spin.setValue(32)
        basic_layout.addRow("批次大小 (Batch Size):", self.batch_size_spin)
        
        self.learning_rate_spin = QDoubleSpinBox()
        self.learning_rate_spin.setRange(0.0001, 0.1)
        self.learning_rate_spin.setSingleStep(0.0001)
        self.learning_rate_spin.setValue(0.001)
        self.learning_rate_spin.setDecimals(4)
        basic_layout.addRow("学习率 (Learning Rate):", self.learning_rate_spin)
        
        layout.addWidget(basic_group)
        
        # 高级参数
        advanced_group = QGroupBox("高级参数")
        advanced_layout = QFormLayout(advanced_group)
        
        self.test_size_spin = QDoubleSpinBox()
        self.test_size_spin.setRange(0.1, 0.5)
        self.test_size_spin.setSingleStep(0.05)
        self.test_size_spin.setValue(0.2)
        self.test_size_spin.setDecimals(2)
        advanced_layout.addRow("测试集比例:", self.test_size_spin)
        
        self.random_state_spin = QSpinBox()
        self.random_state_spin.setRange(0, 9999)
        self.random_state_spin.setValue(42)
        advanced_layout.addRow("随机种子:", self.random_state_spin)
        
        layout.addWidget(advanced_group)
        
        layout.addStretch()
        self.tab_widget.addTab(params_widget, "训练参数")
        
    def create_data_processing_tab(self):
        """创建数据处理配置标签页"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # 数据预处理选项
        preprocessing_group = QGroupBox("数据预处理")
        preprocessing_layout = QVBoxLayout(preprocessing_group)
        
        self.normalize_checkbox = QCheckBox("标准化特征数据")
        self.normalize_checkbox.setChecked(True)
        preprocessing_layout.addWidget(self.normalize_checkbox)
        
        self.shuffle_checkbox = QCheckBox("随机打乱训练数据")
        self.shuffle_checkbox.setChecked(True)
        preprocessing_layout.addWidget(self.shuffle_checkbox)
        
        layout.addWidget(preprocessing_group)
        
        layout.addStretch()
        self.tab_widget.addTab(data_widget, "数据处理")
        
    def on_architecture_changed(self):
        """架构类型改变时的处理"""
        arch_type = self.architecture_combo.currentText()
        
        # 预定义架构配置
        predefined_configs = {
            "简单MLP": {
                "hidden_units": [128, 64],
                "dropout_rate": 0.3
            },
            "深层MLP": {
                "hidden_units": [256, 128, 64, 32],
                "dropout_rate": 0.4
            },
            "宽层MLP": {
                "hidden_units": [512, 256],
                "dropout_rate": 0.5
            }
        }
        
        if arch_type in predefined_configs:
            config = predefined_configs[arch_type]
            self.hidden_layers_edit.setText(", ".join(map(str, config["hidden_units"])))
            self.dropout_spin.setValue(config["dropout_rate"])
            
        self.update_architecture_preview()
        
    def update_architecture_preview(self):
        """更新架构预览"""
        try:
            hidden_text = self.hidden_layers_edit.toPlainText().strip()
            if hidden_text:
                hidden_units = [int(x.strip()) for x in hidden_text.split(',') if x.strip()]
            else:
                hidden_units = []
                
            preview_text = "网络架构预览:\n"
            preview_text += f"输入层: [特征维度]\n"
            
            for i, units in enumerate(hidden_units):
                preview_text += f"隐藏层{i+1}: {units} 神经元 + ReLU + Dropout({self.dropout_spin.value()})\n"
                
            preview_text += f"输出层: [类别数量] 神经元\n"
            preview_text += f"\n总参数估计: 待训练时计算"
            
            self.architecture_preview.setText(preview_text)
            
        except Exception as e:
            self.architecture_preview.setText(f"配置错误: {str(e)}")
            
    def load_current_config(self):
        """加载当前配置"""
        if not self.current_config:
            return
            
        # 加载架构配置
        if 'architecture' in self.current_config:
            arch = self.current_config['architecture']
            index = self.architecture_combo.findText(arch)
            if index >= 0:
                self.architecture_combo.setCurrentIndex(index)
                
        # 加载训练参数
        if 'epochs' in self.current_config:
            self.epochs_spin.setValue(self.current_config['epochs'])
        if 'batch_size' in self.current_config:
            self.batch_size_spin.setValue(self.current_config['batch_size'])
        if 'learning_rate' in self.current_config:
            self.learning_rate_spin.setValue(self.current_config['learning_rate'])
        if 'dropout_rate' in self.current_config:
            self.dropout_spin.setValue(self.current_config['dropout_rate'])
            
        # 加载数据处理选项
        if 'normalize' in self.current_config:
            self.normalize_checkbox.setChecked(self.current_config['normalize'])
        if 'shuffle' in self.current_config:
            self.shuffle_checkbox.setChecked(self.current_config['shuffle'])
            
    def get_config(self):
        """获取配置"""
        try:
            # 解析隐藏层配置
            hidden_text = self.hidden_layers_edit.toPlainText().strip()
            if hidden_text:
                hidden_units = [int(x.strip()) for x in hidden_text.split(',') if x.strip()]
            else:
                hidden_units = [128, 64]  # 默认配置
                
            config = {
                'architecture': self.architecture_combo.currentText(),
                'hidden_units': hidden_units,
                'dropout_rate': self.dropout_spin.value(),
                'epochs': self.epochs_spin.value(),
                'batch_size': self.batch_size_spin.value(),
                'learning_rate': self.learning_rate_spin.value(),
                'test_size': self.test_size_spin.value(),
                'random_state': self.random_state_spin.value(),
                'normalize': self.normalize_checkbox.isChecked(),
                'shuffle': self.shuffle_checkbox.isChecked()
            }
            
            return config
            
        except Exception as e:
            return None

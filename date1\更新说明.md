# TDMS分析器更新说明

## 主要更新内容

### 1. 界面优化
- **复选框选择界面**：将原来的数字列表改为复选框，更直观的选择方式
- **调大字体**：分析方法名称字体从默认大小调整为12号字体，提高可读性
- **滚动支持**：分析方法列表支持滚动，适应更多分析方法

### 2. 多结果显示
- **同时显示**：支持同时显示多种分析结果，不再限制只能显示一种
- **垂直布局**：多个分析结果以垂直布局显示，便于对比查看
- **自动布局**：根据选择的分析方法数量自动调整显示布局

### 3. 保存功能优化
- **用户控制**：分析结果不再自动保存，由用户决定是否保存
- **保存按钮**：在结果面板添加"保存图片"按钮
- **清除功能**：添加"清除结果"按钮，方便重新分析
- **文件命名**：保存的文件名格式为 `tdms_analysis_YYYYMMDD_HHMMSS.png`

### 4. 程序改进
- **窗口大小**：主窗口从1200x800调整为1400x900，提供更多显示空间
- **错误处理**：改进错误处理机制，提供更清晰的错误信息
- **进度显示**：保留进度条显示，提供分析状态反馈

## 使用方法

### 1. 启动程序
```bash
python tdms_analyzer.py
```
或双击 `run_analyzer.bat`

### 2. 选择文件
- 点击"选择TDMS文件"按钮
- 选择要分析的TDMS文件
- 程序会自动加载文件并显示可用通道

### 3. 选择通道
- 从下拉列表中选择要分析的通道
- 设置正确的采样率（默认1000Hz）

### 4. 选择分析方法
- 使用复选框选择需要执行的分析方法
- 可以同时选择多种方法
- 使用"全选"和"清除选择"按钮快速操作

### 5. 开始分析
- 点击"开始分析"按钮
- 等待分析完成，结果会显示在右侧面板

### 6. 保存结果
- 分析完成后，点击"保存图片"按钮保存结果
- 或点击"清除结果"按钮清除当前显示

## 支持的分析方法

1. **时域有量纲特征值**：均值、最大值、最小值、峰值、峰峰值、方差、标准差、RMS
2. **时域无量纲特征值**：偏度、峭度、峰值因子、裕度因子、脉冲因子、波形因子
3. **自相关函数**：信号的自相关分析
4. **互相关函数**：信号的互相关分析
5. **频谱分析**：FFT频谱分析
6. **倒频谱**：信号的倒频谱分析
7. **包络谱**：信号的包络谱分析
8. **阶比谱**：基于转速的阶比分析
9. **功率谱**：信号的功率谱密度
10. **短时傅里叶变换**：时频分析
11. **魏格纳威尔分布**：高分辨率时频分析
12. **小波变换**：多尺度时频分析
13. **本征模函数**：EMD分解分析

## 技术特点

- **多线程处理**：分析过程在后台线程执行，不阻塞界面
- **中文支持**：完全支持中文显示和标签
- **错误恢复**：单个分析方法失败不影响其他方法
- **内存优化**：支持大文件分析，自动优化内存使用
- **高分辨率输出**：保存的图片为300DPI高分辨率

## 系统要求

- Python 3.7+
- 依赖包：numpy, matplotlib, scipy, pywt, nptdms
- 操作系统：Windows/Linux/macOS
- 内存：建议4GB以上
- 显示：建议分辨率1280x720以上

## 注意事项

1. 首次运行可能需要安装依赖包
2. 大文件分析可能需要较长时间
3. 某些分析方法对数据长度有要求
4. 保存的图片文件较大，注意磁盘空间
5. 建议在分析前备份原始数据

## 故障排除

### 常见问题
1. **字体显示异常**：确保系统安装了中文字体
2. **内存不足**：减少同时选择的分析方法数量
3. **分析失败**：检查数据质量和采样率设置
4. **保存失败**：检查磁盘空间和文件权限

### 技术支持
如遇到问题，请检查：
- Python版本和依赖包版本
- TDMS文件格式是否正确
- 系统资源是否充足
- 错误日志信息 
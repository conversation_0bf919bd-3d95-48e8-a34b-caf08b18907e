#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDMS分析器示例使用脚本
演示如何以编程方式使用分析功能
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq, ifft
from scipy.stats import kurtosis, skew
import pywt
from scipy.signal import hilbert
from nptdms import TdmsFile
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TDMSAnalyzerExample:
    """TDMS分析器示例类"""
    
    def __init__(self, sample_rate=1000):
        self.sample_rate = sample_rate
        self.root = tk.Tk()
        self.fig = plt.figure(figsize=(12, 40))
        self.canvas = tk.Canvas(self.root)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.progress = tk.ttk.Progressbar(self.root, orient="horizontal", length=300, mode="determinate")
        self.progress.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)
        
    def load_tdms_file(self, file_path):
        """加载TDMS文件"""
        try:
            self.tdms_data = TdmsFile.read(file_path)
            self.channels = []
            
            for group in self.tdms_data.groups():
                for channel in group.channels():
                    self.channels.append(channel.name)
                    
            print(f"成功加载TDMS文件，发现 {len(self.channels)} 个通道:")
            for i, channel in enumerate(self.channels):
                print(f"  {i+1}. {channel}")
                
            return True
        except Exception as e:
            print(f"加载文件失败: {str(e)}")
            return False
    
    def get_channel_data(self, channel_name):
        """获取指定通道的数据"""
        if not self.tdms_data:
            return None
            
        for group in self.tdms_data.groups():
            for channel in group.channels():
                if channel.name == channel_name:
                    return channel[:]
        return None
    
    def analyze_all_methods(self, channel_name, save_results=True):
        """使用所有13种方法分析指定通道"""
        data = self.get_channel_data(channel_name)
        if data is None:
            print(f"无法获取通道 {channel_name} 的数据")
            return
        
        print(f"\n开始分析通道: {channel_name}")
        print(f"数据长度: {len(data)} 个采样点")
        print(f"采样率: {self.sample_rate} Hz")
        print(f"数据时长: {len(data)/self.sample_rate:.2f} 秒")
        
        # 创建结果图形
        fig, axes = plt.subplots(13, 1, figsize=(12, 40))
        fig.suptitle(f'通道 {channel_name} 的13种时频分析结果', fontsize=16)
        
        # 1. 时域有量纲特征值
        self._analyze_time_domain_features(data, axes[0], "有量纲")
        
        # 2. 时域无量纲特征值
        self._analyze_time_domain_features(data, axes[1], "无量纲")
        
        # 3. 自相关函数
        self._analyze_autocorrelation(data, axes[2])
        
        # 4. 互相关函数
        self._analyze_crosscorrelation(data, axes[3])
        
        # 5. 频谱分析
        self._analyze_spectrum(data, axes[4])
        
        # 6. 倒频谱
        self._analyze_cepstrum(data, axes[5])
        
        # 7. 包络谱
        self._analyze_envelope_spectrum(data, axes[6])
        
        # 8. 阶比谱
        self._analyze_order_spectrum(data, axes[7])
        
        # 9. 功率谱
        self._analyze_power_spectrum(data, axes[8])
        
        # 10. 短时傅里叶变换
        self._analyze_stft(data, axes[9])
        
        # 11. 魏格纳威尔分布
        self._analyze_wigner_ville(data, axes[10])
        
        # 12. 小波变换
        self._analyze_wavelet(data, axes[11])
        
        # 13. 本征模函数
        self._analyze_emd(data, axes[12])
        
        plt.tight_layout()
        
        if save_results:
            filename = f"分析结果_{channel_name}_{self.sample_rate}Hz.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"分析结果已保存到: {filename}")
        
        plt.show()
    
    def _analyze_time_domain_features(self, data, ax, feature_type):
        """时域特征分析"""
        if feature_type == "有量纲":
            features = {
                '均值': np.mean(data),
                '最大值': np.max(data),
                '最小值': np.min(data),
                '峰值': np.max(np.abs(data)),
                '峰峰值': np.max(data) - np.min(data),
                '方差': np.var(data),
                '标准差': np.std(data),
                'RMS': np.sqrt(np.mean(data**2))
            }
        else:  # 无量纲
            features = {
                '偏度': skew(data),
                '峰度': kurtosis(data),
                '波形因子': np.sqrt(np.mean(data**2)) / np.mean(np.abs(data)),
                '峰值因子': np.max(np.abs(data)) / np.sqrt(np.mean(data**2)),
                '脉冲因子': np.max(np.abs(data)) / np.mean(np.abs(data)),
                '裕度因子': np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2,
                '方差因子': np.var(data) / np.mean(data)**2 if np.mean(data) != 0 else 0
            }
        
        names = list(features.keys())
        values = list(features.values())
        
        bars = ax.bar(names, values, color='skyblue', alpha=0.7)
        ax.set_title(f'时域{feature_type}特征值', fontsize=14, fontweight='bold')
        ax.set_ylabel('数值', fontsize=12)
        ax.set_xlabel('特征类型', fontsize=12)
        ax.tick_params(axis='x', rotation=45, labelsize=10)
        ax.tick_params(axis='y', labelsize=10)
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    
    def _analyze_autocorrelation(self, data, ax):
        """自相关函数分析"""
        autocorr = signal.correlate(data, data, mode='full')
        autocorr = autocorr[len(data)-1:]
        autocorr = autocorr / autocorr[0]
        
        time = np.arange(len(autocorr)) / self.sample_rate
        ax.plot(time, autocorr, color='blue', linewidth=1.5)
        ax.set_title('自相关函数', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('自相关系数', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_crosscorrelation(self, data, ax):
        """互相关函数分析"""
        delay = len(data) // 4
        delayed_data = np.roll(data, delay)
        
        crosscorr = signal.correlate(data, delayed_data, mode='full')
        crosscorr = crosscorr[len(data)-1:]
        crosscorr = crosscorr / np.sqrt(np.sum(data**2) * np.sum(delayed_data**2))
        
        time = np.arange(len(crosscorr)) / self.sample_rate
        ax.plot(time, crosscorr, color='green', linewidth=1.5)
        ax.set_title('互相关函数', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('互相关系数', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_spectrum(self, data, ax):
        """频谱分析"""
        fft_data = fft(data)
        freqs = fftfreq(len(data), 1/self.sample_rate)
        
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft_data[:len(freqs)//2])
        
        ax.plot(positive_freqs, positive_fft, color='red', linewidth=1.5)
        ax.set_title('频谱分析', fontsize=14, fontweight='bold')
        ax.set_xlabel('频率 (赫兹)', fontsize=12)
        ax.set_ylabel('幅值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_cepstrum(self, data, ax):
        """倒频谱分析"""
        fft_data = fft(data)
        log_spectrum = np.log(np.abs(fft_data) + 1e-10)
        cepstrum = np.abs(ifft(log_spectrum))
        
        time = np.arange(len(cepstrum)) / self.sample_rate
        ax.plot(time[:len(time)//2], cepstrum[:len(cepstrum)//2], color='purple', linewidth=1.5)
        ax.set_title('倒频谱', fontsize=14, fontweight='bold')
        ax.set_xlabel('倒频率 (秒)', fontsize=12)
        ax.set_ylabel('幅值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_envelope_spectrum(self, data, ax):
        """包络谱分析"""
        analytic_signal = hilbert(data)
        envelope = np.abs(analytic_signal)
        
        fft_envelope = fft(envelope)
        freqs = fftfreq(len(envelope), 1/self.sample_rate)
        
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft_envelope[:len(freqs)//2])
        
        ax.plot(positive_freqs, positive_fft, color='orange', linewidth=1.5)
        ax.set_title('包络谱', fontsize=14, fontweight='bold')
        ax.set_xlabel('频率 (赫兹)', fontsize=12)
        ax.set_ylabel('幅值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_order_spectrum(self, data, ax):
        """阶比谱分析"""
        rpm = 3600
        order_freq = rpm / 60
        
        freqs = fftfreq(len(data), 1/self.sample_rate)
        orders = freqs / order_freq
        
        fft_data = fft(data)
        fft_magnitude = np.abs(fft_data)
        
        positive_orders = orders[:len(orders)//2]
        positive_fft = fft_magnitude[:len(orders)//2]
        
        ax.plot(positive_orders, positive_fft, color='brown', linewidth=1.5)
        ax.set_title('阶比谱', fontsize=14, fontweight='bold')
        ax.set_xlabel('阶比', fontsize=12)
        ax.set_ylabel('幅值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_power_spectrum(self, data, ax):
        """功率谱分析"""
        freqs, psd = signal.welch(data, self.sample_rate, nperseg=1024)
        
        ax.semilogy(freqs, psd, color='darkblue', linewidth=1.5)
        ax.set_title('功率谱密度', fontsize=14, fontweight='bold')
        ax.set_xlabel('频率 (赫兹)', fontsize=12)
        ax.set_ylabel('功率谱密度 (分贝/赫兹)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)
    
    def _analyze_stft(self, data, ax):
        """短时傅里叶变换"""
        f, t, Zxx = signal.stft(data, self.sample_rate, nperseg=256, noverlap=128)
        
        im = ax.pcolormesh(t, f, np.abs(Zxx), shading='gouraud', cmap='viridis')
        ax.set_title('短时傅里叶变换', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('频率 (赫兹)', fontsize=12)
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('幅值', fontsize=10)
        ax.tick_params(labelsize=10)
    
    def _analyze_wigner_ville(self, data, ax):
        """魏格纳威尔分布"""
        f, t, Zxx = signal.stft(data, self.sample_rate, nperseg=256, noverlap=128)
        wvd = np.abs(Zxx)**2
        
        im = ax.pcolormesh(t, f, wvd, shading='gouraud', cmap='plasma')
        ax.set_title('魏格纳威尔分布', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('频率 (赫兹)', fontsize=12)
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('功率', fontsize=10)
        ax.tick_params(labelsize=10)
    
    def _analyze_wavelet(self, data, ax):
        """小波变换"""
        try:
            # 如果数据太长，只使用前一部分进行小波变换
            max_length = 10000  # 限制最大数据长度
            if len(data) > max_length:
                data_subset = data[:max_length]
                print(f"数据过长，使用前{max_length}个采样点进行小波变换")
            else:
                data_subset = data
            
            # 使用更少的尺度，提高计算速度
            scales = np.logspace(0, 3, 32)  # 32个尺度，从1到1000
            wavelet = 'cmor1.5-1.0'
            
            # 计算小波变换
            coefficients, frequencies = pywt.cwt(data_subset, scales, wavelet, 1/self.sample_rate)
            
            # 绘制小波变换结果
            time = np.arange(len(data_subset)) / self.sample_rate
            im = ax.pcolormesh(time, frequencies, np.abs(coefficients), shading='gouraud', cmap='jet')
            ax.set_title('小波变换 (快速模式)', fontsize=14, fontweight='bold')
            ax.set_xlabel('时间 (秒)', fontsize=12)
            ax.set_ylabel('频率 (赫兹)', fontsize=12)
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('幅值', fontsize=10)
            ax.tick_params(labelsize=10)
            
        except Exception as e:
            print(f"小波变换失败: {e}")
            # 如果小波变换失败，显示错误信息
            ax.text(0.5, 0.5, f'小波变换失败: {str(e)}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title('小波变换 (失败)', fontsize=14, fontweight='bold')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
    
    def _analyze_emd(self, data, ax):
        """本征模函数分析"""
        low_freq = 10
        high_freq = 50
        
        b, a = signal.butter(4, [low_freq, high_freq], btype='band', fs=self.sample_rate)
        imf = signal.filtfilt(b, a, data)
        
        time = np.arange(len(imf)) / self.sample_rate
        ax.plot(time, imf, color='teal', linewidth=1.5)
        ax.set_title(f'本征模函数 (IMF) - 频带 {low_freq}-{high_freq} 赫兹', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('幅值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=10)

    def run_analysis(self, selected_indices):
        """运行分析"""
        try:
            data = self.get_selected_data()
            if data is None:
                self.root.after(0, lambda: messagebox.showerror("错误", "无法获取通道数据"))
                return
                
            # 创建结果图形
            n_methods = len(selected_indices)
            fig, axes = plt.subplots(n_methods, 1, figsize=(12, 4*n_methods))
            if n_methods == 1:
                axes = [axes]
            
            for i, idx in enumerate(selected_indices):
                method_name = self.analysis_methods[idx]
                ax = axes[i]
                
                if "时域有量纲特征值" in method_name:
                    self.analyze_time_domain_features(data, ax, "有量纲")
                elif "时域无量纲特征值" in method_name:
                    self.analyze_time_domain_features(data, ax, "无量纲")
                elif "自相关函数" in method_name:
                    self.analyze_autocorrelation(data, ax)
                elif "互相关函数" in method_name:
                    self.analyze_crosscorrelation(data, ax)
                elif "频谱分析" in method_name:
                    self.analyze_spectrum(data, ax)
                elif "倒频谱" in method_name:
                    self.analyze_cepstrum(data, ax)
                elif "包络谱" in method_name:
                    self.analyze_envelope_spectrum(data, ax)
                elif "阶比谱" in method_name:
                    self.analyze_order_spectrum(data, ax)
                elif "功率谱" in method_name:
                    self.analyze_power_spectrum(data, ax)
                elif "短时傅里叶变换" in method_name:
                    self.analyze_stft(data, ax)
                elif "魏格纳威尔分布" in method_name:
                    self.analyze_wigner_ville(data, ax)
                elif "小波变换" in method_name:
                    self.analyze_wavelet(data, ax)
                elif "本征模函数" in method_name:
                    self.analyze_emd(data, ax)
            
            plt.tight_layout()
            
            # 保存结果到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"分析结果_{self.selected_channel}_{timestamp}.png"
            fig.savefig(filename, dpi=300, bbox_inches='tight')
            
            # 直接在面板上显示结果
            self.root.after(0, lambda: self.display_results_in_panel(fig, filename))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("分析错误", str(e)))
        finally:
            self.root.after(0, self.progress.stop)
    
    def display_results_in_panel(self, fig, filename):
        """在面板上显示分析结果"""
        try:
            # 清除原有图形
            self.fig.clear()
            
            # 获取原始图形的子图数量
            n_axes = len(fig.axes)
            
            # 创建新的子图布局
            for i in range(n_axes):
                if i == 0:
                    ax = self.fig.add_subplot(n_axes, 1, i+1)
                else:
                    ax = self.fig.add_subplot(n_axes, 1, i+1, sharex=self.fig.axes[0])
                
                # 获取原始图形的数据
                orig_ax = fig.axes[i]
                
                # 复制标题和标签
                ax.set_title(orig_ax.get_title(), fontsize=12, fontweight='bold')
                ax.set_xlabel(orig_ax.get_xlabel(), fontsize=10)
                ax.set_ylabel(orig_ax.get_ylabel(), fontsize=10)
                ax.grid(True, alpha=0.3)
                
                # 复制图形数据
                try:
                    # 复制线条
                    for line in orig_ax.lines:
                        ax.plot(line.get_xdata(), line.get_ydata(), 
                               color=line.get_color(), linewidth=line.get_linewidth())
                    
                    # 复制柱状图
                    for patch in orig_ax.patches:
                        ax.add_patch(patch)
                    
                    # 复制文本
                    for text in orig_ax.texts:
                        ax.text(text.get_position()[0], text.get_position()[1], 
                               text.get_text(), fontsize=text.get_fontsize())
                    
                    # 复制彩色图
                    for collection in orig_ax.collections:
                        ax.add_collection(collection)
                    
                    # 复制图像
                    for image in orig_ax.images:
                        ax.imshow(image.get_array(), extent=image.get_extent(), 
                                aspect=image.get_aspect(), origin=image.get_origin())
                        
                except Exception as e:
                    # 如果复制失败，显示错误信息
                    ax.text(0.5, 0.5, f'显示错误: {str(e)}', 
                           ha='center', va='center', transform=ax.transAxes)
            
            self.fig.tight_layout()
            self.canvas.draw()
            
            # 显示成功消息
            messagebox.showinfo("分析完成", f"分析完成！\n结果已保存到: {filename}\n\n结果已显示在程序面板中")
            
        except Exception as e:
            # 如果显示失败，显示简单的结果
            self.fig.clear()
            ax = self.fig.add_subplot(111)
            ax.text(0.5, 0.5, f'分析完成\n结果已保存到: {filename}\n\n显示错误: {str(e)}', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title('分析结果')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            self.canvas.draw()
            messagebox.showinfo("分析完成", f"分析完成！\n结果已保存到: {filename}")

def main():
    """主函数 - 示例使用"""
    print("TDMS分析器示例程序")
    print("=" * 50)
    
    # 创建分析器实例
    analyzer = TDMSAnalyzerExample(sample_rate=1000)
    
    # 示例：分析TDMS文件
    # 请将文件路径替换为实际的TDMS文件路径
    file_path = "date2025.07.01--16-04-24.tdms"  # 使用当前目录中的文件
    
    if analyzer.load_tdms_file(file_path):
        # 分析第一个通道
        if analyzer.channels:
            first_channel = analyzer.channels[0]
            print(f"\n分析第一个通道: {first_channel}")
            analyzer.analyze_all_methods(first_channel)
        else:
            print("未找到任何通道")
    else:
        print("文件加载失败")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试故障异常报警系统模块导入
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    try:
        print("正在测试模块导入...")
        
        # 测试PyQt5导入
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import QTimer
        print("✅ PyQt5导入成功")
        
        # 测试样式模块导入
        from ui.styles import (PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, 
                              SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR)
        print("✅ 样式模块导入成功")
        
        # 测试故障异常报警系统导入
        from ui.fault_alarm_system import FaultAlarmSystem, StatusCard, FaultHistoryWindow
        print("✅ 故障异常报警系统模块导入成功")
        
        # 测试创建实例
        app = QApplication([])
        
        # 创建状态卡片
        card = StatusCard("测试卡片", "🔍", "normal")
        print("✅ StatusCard实例创建成功")
        
        # 创建主系统
        alarm_system = FaultAlarmSystem()
        print("✅ FaultAlarmSystem实例创建成功")
        
        # 创建历史记录窗口
        history_window = FaultHistoryWindow()
        print("✅ FaultHistoryWindow实例创建成功")
        
        print("\n🎉 所有测试通过！故障异常报警系统模块工作正常。")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_functionality():
    """测试基本功能"""
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.fault_alarm_system import FaultAlarmSystem
        
        app = QApplication([])
        
        # 创建系统实例
        system = FaultAlarmSystem()
        
        # 测试状态更新
        system.current_status = "normal"
        system.update_status_bar()
        print("✅ 状态更新功能正常")
        
        # 测试时间更新
        system.update_time()
        print("✅ 时间更新功能正常")
        
        # 测试数据模拟
        system.simulate_data_update()
        print("✅ 数据模拟功能正常")
        
        print("\n🎉 功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("故障异常报警系统测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        print("\n" + "=" * 50)
        print("功能测试")
        print("=" * 50)
        
        # 测试功能
        func_success = test_functionality()
        
        if func_success:
            print("\n" + "=" * 50)
            print("测试总结")
            print("=" * 50)
            print("✅ 所有测试通过")
            print("✅ 故障异常报警系统已准备就绪")
            print("\n使用方法：")
            print("1. 在主系统中：通过导航栏访问'故障异常报警'")
            print("2. 独立运行：python test_fault_alarm.py")
            print("3. DL环境：python run_fault_alarm_dl.py")
        else:
            print("\n❌ 功能测试失败")
    else:
        print("\n❌ 导入测试失败")
    
    print("\n" + "=" * 50)

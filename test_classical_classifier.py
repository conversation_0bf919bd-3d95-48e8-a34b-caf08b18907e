#!/usr/bin/env python3
"""
测试经典分类器界面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from ui.classical_classifier import ClassicalClassifier
from ui.styles import PRIMARY_BG


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("经典分类器检测 - 测试界面")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置样式
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {PRIMARY_BG};
            }}
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加经典分类器界面
        self.classifier_widget = ClassicalClassifier(db_manager=None)
        layout.addWidget(self.classifier_widget)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()

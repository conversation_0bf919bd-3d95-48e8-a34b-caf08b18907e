"""
经验模态分解(EMD)工具模块
包含EMD算法的具体实现
"""

import numpy as np
from scipy import signal
from scipy.interpolate import interp1d


class EMDProcessor:
    """EMD处理器类"""
    
    def __init__(self, sample_rate=1000):
        self.sample_rate = sample_rate
    
    def perform_emd(self, data, max_imfs=8):
        """
        执行经验模态分解
        基于参考文件中的EMD算法实现
        
        Args:
            data (array): 输入信号
            max_imfs (int): 最大IMF数量
            
        Returns:
            list: IMF分量列表
        """
        imfs = []
        residue = data.copy()

        for i in range(max_imfs):
            # 提取IMF
            imf = self._extract_imf(residue)

            # 检查是否为有效的IMF
            if imf is None or self._is_monotonic(imf):
                break

            imfs.append(imf)
            residue = residue - imf

            # 如果残差变得单调或者能量很小，停止分解
            if self._is_monotonic(residue) or np.var(residue) < 0.01 * np.var(data):
                break

        # 添加最终残差
        if not self._is_monotonic(residue):
            imfs.append(residue)

        return imfs

    def _extract_imf(self, data, max_iterations=50):
        """提取单个IMF分量"""
        h = data.copy()

        for iteration in range(max_iterations):
            # 找到局部极值点
            maxima_indices, minima_indices = self._find_extrema(h)

            # 如果极值点太少，无法继续
            if len(maxima_indices) < 2 or len(minima_indices) < 2:
                break

            # 构造上下包络线
            upper_envelope = self._create_envelope(h, maxima_indices)
            lower_envelope = self._create_envelope(h, minima_indices)

            if upper_envelope is None or lower_envelope is None:
                break

            # 计算均值包络
            mean_envelope = (upper_envelope + lower_envelope) / 2

            # 更新h
            h_new = h - mean_envelope

            # 检查停止条件
            if self._check_imf_criteria(h, h_new):
                return h_new

            h = h_new

        return h if iteration > 0 else None

    def _find_extrema(self, data):
        """找到局部极值点"""
        # 找到局部最大值和最小值
        maxima = signal.argrelextrema(data, np.greater, order=1)[0]
        minima = signal.argrelextrema(data, np.less, order=1)[0]

        # 确保边界点被包含
        if len(maxima) > 0:
            if maxima[0] != 0:
                maxima = np.insert(maxima, 0, 0)
            if maxima[-1] != len(data) - 1:
                maxima = np.append(maxima, len(data) - 1)

        if len(minima) > 0:
            if minima[0] != 0:
                minima = np.insert(minima, 0, 0)
            if minima[-1] != len(data) - 1:
                minima = np.append(minima, len(data) - 1)

        return maxima, minima

    def _create_envelope(self, data, extrema_indices):
        """创建包络线"""
        if len(extrema_indices) < 2:
            return None

        try:
            # 使用三次样条插值创建包络
            x = extrema_indices
            y = data[extrema_indices]

            # 创建插值函数
            f = interp1d(x, y, kind='cubic', bounds_error=False, fill_value='extrapolate')

            # 生成完整的包络
            envelope = f(np.arange(len(data)))

            return envelope

        except Exception:
            # 如果插值失败，使用线性插值
            envelope = np.interp(np.arange(len(data)), extrema_indices, data[extrema_indices])
            return envelope

    def _check_imf_criteria(self, h_old, h_new, threshold=0.2):
        """检查IMF停止条件"""
        # 计算标准差
        sd = np.sum((h_old - h_new) ** 2) / np.sum(h_old ** 2)
        return sd < threshold

    def _is_monotonic(self, data):
        """检查数据是否单调"""
        diff = np.diff(data)
        return np.all(diff >= 0) or np.all(diff <= 0)

    def _estimate_mean_frequency(self, signal_data):
        """估计信号的平均频率"""
        try:
            # 使用FFT估计主频率
            n = len(signal_data)
            fft_result = np.abs(np.fft.fft(signal_data))
            freqs = np.fft.fftfreq(n, 1/self.sample_rate)

            # 只考虑正频率部分
            positive_freqs = freqs[:n//2]
            positive_fft = fft_result[:n//2]

            # 找到主频率（幅值最大的频率）
            max_idx = np.argmax(positive_fft)
            main_freq = positive_freqs[max_idx]

            return abs(main_freq)
        except:
            return 0.0

    def analyze_emd_hilbert(self, data):
        """使用希尔伯特变换分析EMD分量的时频特性"""
        try:
            from scipy.signal import hilbert
            from scipy.ndimage import gaussian_filter1d
            
            # 执行EMD分解
            imfs = self.perform_emd(data)

            if len(imfs) == 0:
                return {'error': 'EMD分解失败', 'type': 'emd_hilbert'}

            # 计算每个IMF的希尔伯特变换
            # 选择前几个IMF分量进行分析
            n_imfs = min(4, len(imfs))

            # 创建时间轴
            time = np.arange(len(data)) / self.sample_rate

            # 计算每个IMF的瞬时频率和幅值
            inst_freqs = []
            inst_amps = []

            for i in range(n_imfs):
                # 计算希尔伯特变换
                analytic_signal = hilbert(imfs[i])

                # 计算瞬时幅值
                amplitude_envelope = np.abs(analytic_signal)
                inst_amps.append(amplitude_envelope)

                # 计算瞬时相位
                instantaneous_phase = np.unwrap(np.angle(analytic_signal))

                # 计算瞬时频率 (Hz)
                instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.sample_rate
                # 添加一个点使长度匹配
                instantaneous_frequency = np.append(instantaneous_frequency, instantaneous_frequency[-1])

                # 平滑瞬时频率
                instantaneous_frequency = gaussian_filter1d(instantaneous_frequency, sigma=10)

                inst_freqs.append(instantaneous_frequency)

            return {
                'imfs': imfs[:n_imfs],
                'time': time,
                'instantaneous_frequencies': inst_freqs,
                'instantaneous_amplitudes': inst_amps,
                'type': 'emd_hilbert'
            }

        except Exception as e:
            return {'error': str(e), 'type': 'emd_hilbert'}

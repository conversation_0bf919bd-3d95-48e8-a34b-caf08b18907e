#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法分离后的功能
验证algorithms模块的接口和功能是否正常工作
"""

import numpy as np
import matplotlib.pyplot as plt
from algorithms import (
    get_algorithm_interface, 
    analyze_signal_data, 
    diagnose_from_data, 
    complete_analysis_pipeline
)


def generate_test_signal(sample_rate=1000, duration=2.0, noise_level=0.1):
    """
    生成测试信号
    
    Args:
        sample_rate (float): 采样率
        duration (float): 信号持续时间
        noise_level (float): 噪声水平
        
    Returns:
        array: 测试信号数据
    """
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 创建复合信号：基频 + 谐波 + 噪声
    signal = (np.sin(2 * np.pi * 50 * t) +           # 50Hz基频
              0.5 * np.sin(2 * np.pi * 150 * t) +    # 150Hz谐波
              0.3 * np.sin(2 * np.pi * 250 * t) +    # 250Hz谐波
              noise_level * np.random.randn(len(t)))  # 噪声
    
    return signal


def test_algorithm_interface():
    """测试算法接口基本功能"""
    print("=" * 60)
    print("测试算法接口基本功能")
    print("=" * 60)
    
    # 创建算法接口
    interface = get_algorithm_interface(sample_rate=1000)
    
    # 生成测试数据
    test_data = generate_test_signal()
    
    # 测试方法名称获取
    method_names = interface.get_method_names()
    print(f"可用的分析方法数量: {len(method_names)}")
    for i, name in enumerate(method_names):
        print(f"  {i}: {name}")
    
    print("\n✓ 算法接口基本功能测试通过")
    return True


def test_feature_extraction():
    """测试特征提取功能"""
    print("\n" + "=" * 60)
    print("测试特征提取功能")
    print("=" * 60)
    
    # 创建算法接口
    interface = get_algorithm_interface(sample_rate=1000)
    
    # 生成测试数据
    test_data = generate_test_signal()
    
    try:
        # 提取数值特征
        features = interface.extract_numerical_features(test_data)
        
        print(f"提取的特征数量: {len(features)}")
        print("特征列表:")
        for name, value in features.items():
            print(f"  {name}: {value:.6f}")
        
        # 验证特征的合理性
        assert '均值' in features, "缺少均值特征"
        assert '均方根' in features, "缺少均方根特征"
        assert '标准差' in features, "缺少标准差特征"
        assert '峰值因子' in features, "缺少峰值因子特征"
        
        print("\n✓ 特征提取功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 特征提取功能测试失败: {e}")
        return False


def test_signal_analysis():
    """测试信号分析功能"""
    print("\n" + "=" * 60)
    print("测试信号分析功能")
    print("=" * 60)
    
    # 创建算法接口
    interface = get_algorithm_interface(sample_rate=1000)
    
    # 生成测试数据
    test_data = generate_test_signal()
    
    try:
        # 测试几个主要的分析方法
        test_methods = [0, 1, 4, 8]  # 时域有量纲、时域无量纲、频谱、功率谱
        
        for method_idx in test_methods:
            method_name = interface.get_method_names()[method_idx]
            print(f"测试方法: {method_name}")
            
            result = interface.analyze_signal(test_data, method_idx)
            
            if 'error' in result:
                print(f"  ✗ 分析失败: {result['error']}")
            else:
                print(f"  ✓ 分析成功，结果类型: {result.get('type', 'unknown')}")
        
        print("\n✓ 信号分析功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 信号分析功能测试失败: {e}")
        return False


def test_fault_diagnosis():
    """测试故障诊断功能"""
    print("\n" + "=" * 60)
    print("测试故障诊断功能")
    print("=" * 60)
    
    # 创建算法接口
    interface = get_algorithm_interface(sample_rate=1000)
    
    # 生成测试数据
    test_data = generate_test_signal()
    
    try:
        # 提取特征
        features = interface.extract_numerical_features(test_data)
        
        # 设置正常状态基线
        interface.set_normal_baseline(features)
        
        # 测试不同的诊断方法
        diagnosis_methods = ['threshold', 'multi_mahalanobis', 'single_mahalanobis']

        for method in diagnosis_methods:
            print(f"测试诊断方法: {method}")

            try:
                if method == 'single_mahalanobis':
                    # 单特征诊断只使用一个特征
                    single_feature = {'均方根': features['均方根']}
                    result = interface.diagnose_fault(single_feature, method)
                else:
                    result = interface.diagnose_fault(features, method)

                # 检查结果状态
                status = result.get('overall_status') or result.get('classification_result')
                if status and 'error' not in status:
                    print(f"  ✓ 诊断成功，状态: {status}")

                    # 获取诊断摘要
                    summary = interface.get_diagnosis_summary(result)
                    print(f"  摘要: {summary.split(chr(10))[0]}")  # 只显示第一行
                else:
                    print(f"  ✗ 诊断失败: {result.get('message', '状态异常')}")

            except Exception as e:
                print(f"  ✗ 诊断异常: {e}")
        
        print("\n✓ 故障诊断功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 故障诊断功能测试失败: {e}")
        return False


def test_convenience_functions():
    """测试便捷函数"""
    print("\n" + "=" * 60)
    print("测试便捷函数")
    print("=" * 60)
    
    # 生成测试数据
    test_data = generate_test_signal()
    
    try:
        # 测试analyze_signal_data
        print("测试 analyze_signal_data 函数")
        results = analyze_signal_data(test_data, method_indices=[0, 1, 4], sample_rate=1000)
        print(f"  分析结果数量: {len(results)}")
        
        # 测试diagnose_from_data
        print("测试 diagnose_from_data 函数")
        diagnosis = diagnose_from_data(test_data, method='threshold', sample_rate=1000)
        print(f"  诊断状态: {diagnosis.get('overall_status', 'unknown')}")
        
        # 测试complete_analysis_pipeline
        print("测试 complete_analysis_pipeline 函数")
        pipeline_result = complete_analysis_pipeline(test_data, sample_rate=1000)
        print(f"  流水线结果包含: {list(pipeline_result.keys())}")
        
        print("\n✓ 便捷函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 便捷函数测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("轴承故障诊断系统 - 算法分离测试")
    print("测试算法和界面分离后的功能完整性")
    
    # 运行所有测试
    tests = [
        test_algorithm_interface,
        test_feature_extraction,
        test_signal_analysis,
        test_fault_diagnosis,
        test_convenience_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 出现异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    
    if passed == total:
        print("\n🎉 所有测试通过！算法分离成功！")
        print("✓ 算法模块独立运行正常")
        print("✓ 特征提取功能完整")
        print("✓ 故障诊断功能完整")
        print("✓ 接口设计合理")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

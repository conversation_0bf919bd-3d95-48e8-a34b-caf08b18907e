#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.login_window import <PERSON>ginWindow

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    
    # 设置一个定时器来保持窗口显示
    timer = QTimer()
    timer.timeout.connect(lambda: None)
    timer.start(1000)
    
    print("登录界面已显示，按 Ctrl+C 退出")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)

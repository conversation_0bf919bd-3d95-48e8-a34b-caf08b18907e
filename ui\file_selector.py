"""
TDMS文件选择界面模块
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QTableWidget, QTableWidgetItem, QPushButton,
                             QLineEdit, QComboBox, QGroupBox, QMessageBox,
                             QProgressBar, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from ui.styles import ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR
import logging

logger = logging.getLogger(__name__)


class FileLoadThread(QThread):
    """文件加载线程"""
    files_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, db_manager, search_params=None):
        super().__init__()
        self.db_manager = db_manager
        self.search_params = search_params or {}
    
    def run(self):
        """执行文件加载"""
        try:
            if self.search_params:
                files = self.db_manager.search_files(**self.search_params)
            else:
                files = self.db_manager.get_tdms_files()
            self.files_loaded.emit(files)
        except Exception as e:
            self.error_occurred.emit(str(e))


class FileSelector(QWidget):
    """TDMS文件选择器"""
    file_selected = pyqtSignal(dict)  # 文件选择信号
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.current_files = []
        self.selected_file = None
        self.load_thread = None
        self.init_ui()
        self.load_files()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)  # 减小边距
        layout.setSpacing(15)  # 减小间距

        # 标题
        title_label = QLabel("TDMS文件选择")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        title_label.setMaximumHeight(40)  # 限制标题高度
        layout.addWidget(title_label)

        # 搜索区域
        search_group = self.create_search_group()
        search_group.setMaximumHeight(180)  # 限制搜索区域高度
        layout.addWidget(search_group)

        # 文件列表区域 - 设置拉伸因子，让它占用更多空间
        file_group = self.create_file_list_group()
        layout.addWidget(file_group, 1)  # 拉伸因子为1，占用剩余空间

        # 操作按钮区域
        button_layout = self.create_button_layout()
        layout.addLayout(button_layout)
    
    def create_search_group(self):
        """创建搜索区域"""
        group = QGroupBox("搜索筛选")
        group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                border: 2px solid #3a3a4a;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                font-family: 'Microsoft YaHei';
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {ACCENT_COLOR};
            }}
        """)

        layout = QVBoxLayout(group)
        layout.setContentsMargins(10, 15, 10, 10)
        layout.setSpacing(12)

        # 筛选条件行
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(20)

        # 测试时间筛选
        test_date_label = QLabel("测试时间:")
        test_date_label.setMinimumWidth(80)
        test_date_label.setFont(QFont('Microsoft YaHei', 12))
        filter_layout.addWidget(test_date_label)

        self.test_date_combo = QComboBox()
        self.test_date_combo.setFont(QFont('Microsoft YaHei', 12))
        self.test_date_combo.setMinimumHeight(32)
        self.test_date_combo.setMaximumHeight(32)
        self.test_date_combo.setFixedWidth(160)  # 固定宽度，避免超出
        self.test_date_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 6px 12px;
                color: #495057;
            }}
            QComboBox:hover {{
                border-color: {ACCENT_COLOR};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
            }}
        """)
        # 暂时断开信号连接，避免加载时触发搜索
        filter_layout.addWidget(self.test_date_combo)

        # 添加间距
        filter_layout.addSpacing(20)

        # 部件筛选
        component_label = QLabel("部件:")
        component_label.setMinimumWidth(60)
        component_label.setFont(QFont('Microsoft YaHei', 12))
        filter_layout.addWidget(component_label)

        self.component_combo = QComboBox()
        self.component_combo.setFont(QFont('Microsoft YaHei', 12))
        self.component_combo.setMinimumHeight(32)
        self.component_combo.setMaximumHeight(32)
        self.component_combo.setFixedWidth(120)  # 固定宽度，避免超出
        self.component_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 6px 12px;
                color: #495057;
            }}
            QComboBox:hover {{
                border-color: {ACCENT_COLOR};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
            }}
        """)
        self.component_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 6px 12px;
                color: #495057;
            }}
            QComboBox:hover {{
                border-color: {ACCENT_COLOR};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
            }}
        """)
        filter_layout.addWidget(self.component_combo)

        # 添加弹性空间
        filter_layout.addStretch()

        layout.addLayout(filter_layout)

        # 加载下拉选项数据
        self.load_filter_options()

        return group

    def load_filter_options(self):
        """加载筛选选项数据"""
        try:
            # 加载测试时间选项
            test_dates = self.db_manager.get_test_dates()
            self.test_date_combo.clear()
            self.test_date_combo.addItem("全部")
            self.test_date_combo.addItems(test_dates)

            # 加载部件选项
            components = self.db_manager.get_components()
            self.component_combo.clear()
            self.component_combo.addItem("全部")
            self.component_combo.addItems(components)

            # 加载完成后连接信号，避免加载时触发搜索
            self.test_date_combo.currentTextChanged.connect(self.on_filter_changed)
            self.component_combo.currentTextChanged.connect(self.on_filter_changed)

        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载筛选选项失败: {e}")

    def on_filter_changed(self):
        """筛选条件变化时自动搜索"""
        # 添加防抖机制，避免频繁查询
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()

        from PyQt5.QtCore import QTimer
        self._filter_timer = QTimer()
        self._filter_timer.setSingleShot(True)
        self._filter_timer.timeout.connect(self.search_files)
        self._filter_timer.start(300)  # 300ms延迟
    
    def create_file_list_group(self):
        """创建文件列表区域"""
        group = QGroupBox("TDMS文件列表")
        group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                border: 2px solid #43BC24;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
                font-family: 'Microsoft YaHei';
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {ACCENT_COLOR};
            }}
        """)

        layout = QVBoxLayout(group)
        layout.setContentsMargins(10, 20, 10, 10)  # 设置内容边距

        # 状态标签
        self.status_label = QLabel("正在加载文件列表...")
        self.status_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 12px;")
        layout.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)  # 限制进度条高度
        layout.addWidget(self.progress_bar)

        # 文件表格
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(7)
        self.file_table.setHorizontalHeaderLabels([
            "ID", "测试时间", "车型", "部件", "传感器类型", "传感器编号", "文件路径"
        ])

        # 设置表格属性
        self.file_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.file_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.file_table.setAlternatingRowColors(True)

        # 设置表格尺寸和滚动
        self.file_table.setMinimumHeight(300)  # 设置最小高度
        self.file_table.setMaximumHeight(450)  # 设置最大高度，防止超出页面
        self.file_table.setSizeAdjustPolicy(QAbstractItemView.AdjustToContents)

        # 设置列宽
        header = self.file_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 50)   # ID列 - 稍微缩小
        header.resizeSection(1, 140)  # 时间列 - 稍微缩小
        header.resizeSection(2, 80)   # 车型列 - 缩小
        header.resizeSection(3, 80)   # 部件列 - 缩小
        header.resizeSection(4, 90)   # 传感器类型列 - 稍微缩小
        header.resizeSection(5, 90)   # 传感器编号列 - 稍微缩小

        # 设置表格字体和行高
        self.file_table.setFont(QFont('Microsoft YaHei', 12))  # 稍微减小字体
        self.file_table.verticalHeader().setDefaultSectionSize(32)  # 稍微减小行高
        header.setFont(QFont('Microsoft YaHei', 13, QFont.Bold))    # 表头字体

        # 设置表格样式 - 清新协调的配色方案
        self.file_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: #f8f9fa;
                alternate-background-color: #e9ecef;
                selection-background-color: {ACCENT_COLOR};
                selection-color: white;
                gridline-color: #dee2e6;
                color: #495057;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-family: 'Microsoft YaHei';
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border: none;
                border-bottom: 1px solid #e9ecef;
            }}
            QTableWidget::item:selected {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
            QTableWidget::item:hover {{
                background-color: #e3f2fd;
            }}
            QHeaderView::section {{
                background-color: #11999e;
                color: white;
                padding: 14px 8px;
                border: none;
                border-right: 1px solid #2d3e50;
                font-weight: bold;
                font-size: 12px;
            }}
            QHeaderView::section:hover {{
                background-color: #2d3e50;
            }}
            QScrollBar:vertical {{
                background-color: #f1f3f4;
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {ACCENT_COLOR};
            }}
        """)

        # 连接选择事件
        self.file_table.itemSelectionChanged.connect(self.on_file_selected)
        self.file_table.itemDoubleClicked.connect(self.on_file_double_clicked)

        layout.addWidget(self.file_table)

        return group
    
    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 10, 0, 0)  # 设置上边距

        # 选择文件按钮
        self.select_button = QPushButton("选择此文件")
        self.select_button.setEnabled(False)
        self.select_button.setStyleSheet(f"""
            QPushButton {{
                min-width: 100px;
                min-height: 32px;
                max-height: 32px;
                font-size: 13px;
                font-weight: bold;
                background-color: {SUCCESS_COLOR};
                border-radius: 4px;
                padding: 6px 12px;
            }}
            QPushButton:hover {{
                background-color: #00c9a7;
            }}
            QPushButton:disabled {{
                background-color: #4a4a5a;
                color: #888888;
            }}
        """)
        self.select_button.setFont(QFont('Microsoft YaHei', 13))
        self.select_button.clicked.connect(self.confirm_selection)
        layout.addWidget(self.select_button)

        layout.addStretch()

        # 文件信息标签
        self.info_label = QLabel("请选择一个TDMS文件")
        self.info_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 11px;
            font-family: 'Microsoft YaHei';
            padding: 5px;
        """)
        self.info_label.setWordWrap(True)  # 允许文字换行
        layout.addWidget(self.info_label)

        return layout
    
    def load_files(self):
        """加载文件列表"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在加载文件列表...")
        self.file_table.setRowCount(0)
        
        # 创建并启动加载线程
        self.load_thread = FileLoadThread(self.db_manager)
        self.load_thread.files_loaded.connect(self.on_files_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()
    
    def search_files(self):
        """搜索文件"""
        # 构建搜索参数
        search_params = {}
        
        # 测试时间筛选
        if self.test_date_combo.currentText() != "全部":
            search_params['测试时间'] = self.test_date_combo.currentText()

        # 部件筛选
        if self.component_combo.currentText() != "全部":
            search_params['部件'] = self.component_combo.currentText()
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.status_label.setText("正在搜索文件...")
        self.file_table.setRowCount(0)
        
        # 创建并启动搜索线程
        self.load_thread = FileLoadThread(self.db_manager, search_params)
        self.load_thread.files_loaded.connect(self.on_files_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()
    
    def reset_search(self):
        """重置搜索条件"""
        self.test_date_combo.setCurrentIndex(0)  # 设置为"全部"
        self.component_combo.setCurrentIndex(0)  # 设置为"全部"
        self.load_files()
    
    def on_files_loaded(self, files):
        """文件加载完成"""
        self.progress_bar.setVisible(False)
        self.current_files = files
        
        if not files:
            self.status_label.setText("未找到TDMS文件")
            self.file_table.setRowCount(0)
            return
        
        self.status_label.setText(f"共找到 {len(files)} 个TDMS文件")
        
        # 填充表格
        self.file_table.setRowCount(len(files))
        for row, file_info in enumerate(files):
            self.file_table.setItem(row, 0, QTableWidgetItem(str(file_info.get('ID', ''))))
            self.file_table.setItem(row, 1, QTableWidgetItem(str(file_info.get('测试时间', ''))))
            self.file_table.setItem(row, 2, QTableWidgetItem(str(file_info.get('车型', ''))))
            self.file_table.setItem(row, 3, QTableWidgetItem(str(file_info.get('部件', ''))))
            self.file_table.setItem(row, 4, QTableWidgetItem(str(file_info.get('传感器类型', ''))))
            self.file_table.setItem(row, 5, QTableWidgetItem(str(file_info.get('传感器编号', ''))))
            self.file_table.setItem(row, 6, QTableWidgetItem(str(file_info.get('完整路径', ''))))
    
    def on_load_error(self, error_message):
        """文件加载错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"加载失败: {error_message}")
        self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 12px;")
        
        QMessageBox.warning(self, "错误", f"加载TDMS文件列表失败:\n{error_message}")
    
    def on_file_selected(self):
        """文件选择事件"""
        current_row = self.file_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_files):
            self.selected_file = self.current_files[current_row]
            self.select_button.setEnabled(True)
            
            # 更新信息标签
            file_path = self.selected_file.get('完整路径', '')
            self.info_label.setText(f"已选择: {file_path}")
            self.info_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 12px;")
        else:
            self.selected_file = None
            self.select_button.setEnabled(False)
            self.info_label.setText("请选择一个TDMS文件")
            self.info_label.setStyleSheet(f"color: {TEXT_SECONDARY}; font-size: 12px;")
    
    def on_file_double_clicked(self, item):
        """文件双击事件"""
        self.confirm_selection()
    
    def confirm_selection(self):
        """确认选择"""
        if self.selected_file:
            # 发送文件选择信号
            self.file_selected.emit(self.selected_file)

            # 显示确认消息
            QMessageBox.information(self, "文件选择",
                                  f"已选择文件:\n{self.selected_file.get('完整路径', '')}")
        else:
            QMessageBox.warning(self, "警告", "请先选择一个TDMS文件")

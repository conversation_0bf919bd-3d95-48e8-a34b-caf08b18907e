-- 地面数据分析决策系统数据库表结构
-- 轴承故障诊断系统

-- 创建数据库
CREATE DATABASE IF NOT EXISTS music_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE music_db;

-- TDMS文件信息表
CREATE TABLE IF NOT EXISTS tdms (
    ID INT AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
    测试时间 DATETIME NOT NULL COMMENT '测试时间',
    车型 VARCHAR(100) NOT NULL COMMENT '车型',
    部件 VARCHAR(100) NOT NULL COMMENT '部件名称',
    传感器类型 VARCHAR(50) NOT NULL COMMENT '传感器类型',
    传感器编号 VARCHAR(50) NOT NULL COMMENT '传感器编号',
    TDMS文件路径 VARCHAR(500) NOT NULL COMMENT 'TDMS文件存储路径',
    TDMS文件名 VARCHAR(200) NOT NULL COMMENT 'TDMS文件名（不含扩展名）',
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    INDEX idx_test_time (测试时间),
    INDEX idx_vehicle (车型),
    INDEX idx_component (部件),
    INDEX idx_sensor_type (传感器类型),
    INDEX idx_sensor_id (传感器编号)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='TDMS文件信息表';

-- 插入示例数据
INSERT INTO tdms (测试时间, 车型, 部件, 传感器类型, 传感器编号, TDMS文件路径, TDMS文件名) VALUES
('2025-07-07 10:30:00', 'vehicle model', 'Engine, transmission', '316F10,316F10', 'How do you do?', 'D:\\Desktop\\labview_shuju\\', 'date2025.07.07--20-16-14'),
('2025-07-07 15:45:00', 'vehicle model', 'Engine, transmission', '316F10,316F10', 'How do you do?', 'D:\\Desktop\\labview_shuju\\', 'date2025.07.07--20-15-57'),
('2025-07-03 09:20:00', 'vehicle model', 'Engine, transmission', '316F10,316F10', 'How do you do?', 'D:\\Desktop\\labview_shuju\\', 'date2025.07.03--20-59-31'),
('2025-07-03 14:15:00', 'vehicle model', 'Engine, transmission', '316F10,316F10', 'How do you do?', 'D:\\Desktop\\labview_shuju\\', 'date2025.07.03--20-53-12');

-- 用户表（用于登录认证）
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    real_name VARCHAR(100) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('admin', 'user', 'viewer') DEFAULT 'user' COMMENT '用户角色',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '用户状态',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员用户（密码：admin）
INSERT INTO users (username, password, real_name, role) VALUES
('admin', 'admin', '系统管理员', 'admin');

-- 故障诊断结果表
CREATE TABLE IF NOT EXISTS diagnosis_results (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '诊断结果ID',
    file_id INT NOT NULL COMMENT '关联的TDMS文件ID',
    diagnosis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '诊断时间',
    algorithm_type VARCHAR(50) NOT NULL COMMENT '算法类型（经典/深度学习）',
    algorithm_name VARCHAR(100) NOT NULL COMMENT '具体算法名称',
    fault_type VARCHAR(100) COMMENT '故障类型',
    confidence DECIMAL(5,4) COMMENT '置信度',
    status ENUM('normal', 'warning', 'fault') NOT NULL COMMENT '诊断状态',
    details JSON COMMENT '详细诊断信息',
    created_by INT COMMENT '诊断执行用户',
    FOREIGN KEY (file_id) REFERENCES tdms(ID) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_file_id (file_id),
    INDEX idx_diagnosis_time (diagnosis_time),
    INDEX idx_algorithm_type (algorithm_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='故障诊断结果表';

-- 特征提取结果表
CREATE TABLE IF NOT EXISTS feature_extraction (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '特征提取ID',
    file_id INT NOT NULL COMMENT '关联的TDMS文件ID',
    feature_type VARCHAR(50) NOT NULL COMMENT '特征类型',
    feature_name VARCHAR(100) NOT NULL COMMENT '特征名称',
    feature_value DECIMAL(15,6) COMMENT '特征值',
    extraction_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提取时间',
    parameters JSON COMMENT '提取参数',
    FOREIGN KEY (file_id) REFERENCES tdms(ID) ON DELETE CASCADE,
    INDEX idx_file_id (file_id),
    INDEX idx_feature_type (feature_type),
    INDEX idx_extraction_time (extraction_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='特征提取结果表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('system_name', '地面数据分析决策系统', 'string', '系统名称'),
('version', '1.0', 'string', '系统版本'),
('max_file_size', '1073741824', 'integer', '最大文件大小（字节）'),
('default_algorithm', 'SVM', 'string', '默认分类算法'),
('auto_backup', 'true', 'boolean', '是否自动备份');

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id INT COMMENT '操作用户ID',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    target_type VARCHAR(50) COMMENT '操作对象类型',
    target_id INT COMMENT '操作对象ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_operation (operation),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 创建视图：完整的TDMS文件信息
CREATE VIEW v_tdms_files_complete AS
SELECT 
    t.*,
    CONCAT(t.TDMS文件路径, t.TDMS文件名, '.tdms') AS 完整路径,
    COUNT(d.id) AS 诊断次数,
    MAX(d.diagnosis_time) AS 最后诊断时间
FROM tdms t
LEFT JOIN diagnosis_results d ON t.ID = d.file_id
GROUP BY t.ID;

-- 创建视图：用户操作统计
CREATE VIEW v_user_statistics AS
SELECT 
    u.id,
    u.username,
    u.real_name,
    COUNT(DISTINCT d.id) AS 诊断次数,
    COUNT(DISTINCT l.id) AS 操作次数,
    MAX(u.last_login) AS 最后登录时间
FROM users u
LEFT JOIN diagnosis_results d ON u.id = d.created_by
LEFT JOIN operation_logs l ON u.id = l.user_id
GROUP BY u.id;

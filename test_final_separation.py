#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试 - 算法和界面分离后的完整功能测试
验证所有功能正常工作，算法独立性，界面调用正确性
"""

import numpy as np
import sys
import os

def test_algorithm_independence():
    """测试算法独立性"""
    print("=" * 60)
    print("测试算法模块独立性")
    print("=" * 60)
    
    try:
        # 测试算法模块可以独立导入和使用
        from algorithms import (
            get_algorithm_interface,
            FeatureExtractor,
            FaultDiagnosisAlgorithm,
            SignalProcessor,
            analyze_signal_data,
            diagnose_from_data,
            complete_analysis_pipeline
        )
        
        print("✓ 算法模块独立导入成功")
        
        # 测试算法接口
        interface = get_algorithm_interface(sample_rate=1000)
        print("✓ 算法接口创建成功")
        
        # 生成测试数据
        test_data = np.random.randn(2000) + 0.1 * np.sin(2 * np.pi * 50 * np.linspace(0, 2, 2000))
        
        # 测试特征提取
        features = interface.extract_numerical_features(test_data)
        print(f"✓ 特征提取成功，提取了 {len(features)} 个特征")
        
        # 测试故障诊断
        interface.set_normal_baseline(features)
        diagnosis = interface.diagnose_fault(features, 'threshold')
        print(f"✓ 故障诊断成功，状态: {diagnosis.get('overall_status')}")
        
        # 测试便捷函数
        pipeline_result = complete_analysis_pipeline(test_data, sample_rate=1000)
        print("✓ 完整分析流水线测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 算法独立性测试失败: {e}")
        return False


def test_ui_algorithm_integration():
    """测试UI与算法的集成"""
    print("\n" + "=" * 60)
    print("测试UI与算法集成")
    print("=" * 60)
    
    try:
        # 测试UI模块能正确导入算法接口
        from ui.feature_analysis import FeatureAnalysis
        from ui.fault_diagnosis import FaultDiagnosis
        print("✓ UI模块导入成功")
        
        # 测试算法接口在UI中的使用
        from algorithms import get_algorithm_interface
        interface = get_algorithm_interface()
        
        # 验证方法名称获取
        method_names = interface.get_method_names()
        print(f"✓ 获取分析方法名称成功，共 {len(method_names)} 种方法")
        
        # 验证所有方法都可用
        expected_methods = [
            "时域有量纲特征值", "时域无量纲特征值", "自相关函数", "互相关函数",
            "频谱分析", "倒频谱", "包络谱", "阶比谱", "功率谱",
            "短时傅里叶变换", "魏格纳威尔分布", "小波变换", "本征模函数"
        ]
        
        for expected in expected_methods:
            if expected in method_names:
                print(f"  ✓ {expected}")
            else:
                print(f"  ✗ 缺少方法: {expected}")
        
        return True
        
    except Exception as e:
        print(f"✗ UI算法集成测试失败: {e}")
        return False


def test_algorithm_methods():
    """测试各种算法方法"""
    print("\n" + "=" * 60)
    print("测试算法方法完整性")
    print("=" * 60)
    
    try:
        from algorithms import get_algorithm_interface
        interface = get_algorithm_interface(sample_rate=1000)
        
        # 生成测试信号
        t = np.linspace(0, 2, 2000)
        test_signal = (np.sin(2 * np.pi * 10 * t) + 
                      0.5 * np.sin(2 * np.pi * 50 * t) + 
                      0.1 * np.random.randn(len(t)))
        
        # 测试所有分析方法
        method_names = interface.get_method_names()
        success_count = 0
        
        for i, method_name in enumerate(method_names):
            try:
                result = interface.analyze_signal(test_signal, i)
                if 'error' not in result:
                    print(f"  ✓ {method_name}")
                    success_count += 1
                else:
                    print(f"  ✗ {method_name}: {result['error']}")
            except Exception as e:
                print(f"  ✗ {method_name}: {str(e)}")
        
        print(f"\n成功率: {success_count}/{len(method_names)} ({success_count/len(method_names)*100:.1f}%)")
        
        return success_count >= len(method_names) * 0.8  # 80%成功率
        
    except Exception as e:
        print(f"✗ 算法方法测试失败: {e}")
        return False


def test_fault_diagnosis_methods():
    """测试故障诊断方法"""
    print("\n" + "=" * 60)
    print("测试故障诊断方法")
    print("=" * 60)
    
    try:
        from algorithms import get_algorithm_interface
        interface = get_algorithm_interface()
        
        # 生成测试数据
        normal_data = np.random.randn(1000) * 0.1
        fault_data = np.random.randn(1000) * 0.5 + 0.3
        
        # 提取特征
        normal_features = interface.extract_numerical_features(normal_data)
        fault_features = interface.extract_numerical_features(fault_data)
        
        # 设置正常基线
        interface.set_normal_baseline(normal_features)
        
        # 测试不同诊断方法
        diagnosis_methods = ['threshold', 'multi_mahalanobis', 'single_mahalanobis']
        
        for method in diagnosis_methods:
            try:
                if method == 'single_mahalanobis':
                    # 单特征诊断
                    single_feature = {'均方根': fault_features['均方根']}
                    result = interface.diagnose_fault(single_feature, method)
                else:
                    result = interface.diagnose_fault(fault_features, method)
                
                status = result.get('overall_status') or result.get('classification_result')
                print(f"  ✓ {method}: {status}")
                
            except Exception as e:
                print(f"  ✗ {method}: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 故障诊断方法测试失败: {e}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n" + "=" * 60)
    print("验证文件结构")
    print("=" * 60)
    
    # 检查算法文件夹结构
    algorithm_files = [
        'algorithms/__init__.py',
        'algorithms/feature_extraction.py',
        'algorithms/fault_diagnosis.py',
        'algorithms/signal_processing.py',
        'algorithms/emd_utils.py'
    ]
    
    for file_path in algorithm_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ✗ 缺少文件: {file_path}")
    
    # 检查ui/analysis_methods.py是否已删除
    if not os.path.exists('ui/analysis_methods.py'):
        print("  ✓ ui/analysis_methods.py 已成功删除")
    else:
        print("  ✗ ui/analysis_methods.py 仍然存在")
    
    # 检查UI文件
    ui_files = [
        'ui/feature_analysis.py',
        'ui/fault_diagnosis.py'
    ]
    
    for file_path in ui_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ✗ 缺少文件: {file_path}")
    
    return True


def main():
    """主测试函数"""
    print("轴承故障诊断系统 - 算法界面分离最终验证")
    print("验证算法和界面完全分离后的系统完整性")
    
    tests = [
        ("算法独立性", test_algorithm_independence),
        ("UI算法集成", test_ui_algorithm_integration),
        ("算法方法完整性", test_algorithm_methods),
        ("故障诊断方法", test_fault_diagnosis_methods),
        ("文件结构", test_file_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name} 测试通过")
            else:
                print(f"\n✗ {test_name} 测试失败")
        except Exception as e:
            print(f"\n✗ {test_name} 测试异常: {e}")
    
    # 最终总结
    print("\n" + "=" * 60)
    print("最终验证总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 算法和界面分离完全成功！")
        print("✓ 算法模块完全独立")
        print("✓ UI模块只包含界面逻辑")
        print("✓ 算法通过接口调用")
        print("✓ 代码结构清晰，易于维护")
        print("✓ 算法可以独立修改而不影响界面")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

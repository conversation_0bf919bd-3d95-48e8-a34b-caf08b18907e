#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的多特征马氏距离分类子界面
验证三个图表是否都能正确显示
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fixed_mahalanobis_dialog():
    """测试修复后的马氏距离分类子界面"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("修复后的马氏距离分类测试")
    main_window.resize(500, 400)
    
    # 设置窗口样式
    main_window.setStyleSheet("""
        QMainWindow {
            background-color: #1e1e2e;
            color: white;
            font-family: 'Microsoft YaHei';
        }
    """)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(20)
    layout.setContentsMargins(30, 30, 30, 30)
    
    # 标题
    title_label = QLabel("修复后的多特征马氏距离分类测试")
    title_label.setStyleSheet("""
        QLabel {
            font-size: 18px;
            font-weight: bold;
            color: #6c5ce7;
            margin-bottom: 20px;
        }
    """)
    title_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(title_label)
    
    # 说明文字
    info_label = QLabel("""
    修复内容：
    1. 增加了详细的错误处理和调试信息
    2. 修复了图表创建时的数据验证
    3. 删除了多特征马氏距离分类分析图
    4. 确保两个图表都能正确显示：
       - 特征空间分析表格
       - 马氏距离对比图表
    
    测试步骤：
    1. 点击下方按钮打开子界面
    2. 检查是否显示了两个图表
    3. 查看控制台输出的调试信息
    """)
    info_label.setStyleSheet("""
        QLabel {
            font-size: 12px;
            color: #a0a0a0;
            background-color: #2d2d3d;
            padding: 15px;
            border-radius: 8px;
            line-height: 1.5;
        }
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试修复后的子界面")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c5ce7;
            color: white;
            font-size: 14px;
            font-weight: 500;
            min-height: 50px;
            padding: 15px 25px;
            border-radius: 10px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
        QPushButton:hover {
            background-color: #5a4fcf;
        }
        QPushButton:pressed {
            background-color: #4834d4;
        }
    """)
    
    # 结果显示区域
    result_label = QLabel("点击按钮开始测试...")
    result_label.setStyleSheet("""
        QLabel {
            font-size: 12px;
            color: #00d4aa;
            background-color: #2d2d3d;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #4d4d6d;
        }
    """)
    result_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(result_label)
    
    def open_dialog():
        """打开马氏距离分类子界面"""
        try:
            result_label.setText("正在打开子界面...")
            app.processEvents()
            
            print("=" * 60)
            print("开始测试修复后的马氏距离分类子界面")
            print("=" * 60)
            
            from ui.fault_diagnosis import MultiFeatureMahalanobisDialog
            
            # 创建测试特征数据
            test_features = {
                '均方根': 1.750195,
                '偏度': -0.043924,
                '峰度': -1.245920,
                '峰值因子': 1.938262,
                '裕度因子': 2.530399,
                '脉冲因子': 2.246699
            }
            
            print(f"测试特征: {list(test_features.keys())}")
            print(f"特征值: {list(test_features.values())}")
            
            result_label.setText(f"测试特征: {', '.join(test_features.keys())}")
            app.processEvents()
            
            # 创建并显示子界面
            print("创建子界面...")
            dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
            
            print("显示子界面...")
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("用户关闭了子界面")
                result_label.setText("✓ 子界面测试完成")
                
                results = dialog.get_results()
                if results:
                    classification = results.get('classification_result', 'unknown')
                    pos_dist = results.get('mahalanobis_distance_positive', 'N/A')
                    neg_dist = results.get('mahalanobis_distance_negative', 'N/A')
                    
                    result_text = f"""
✓ 修复测试完成
分类结果: {classification}
到正样本距离: {pos_dist}
到负样本距离: {neg_dist}

请检查子界面是否显示了两个图表：
1. 特征空间分析表格 ✓
2. 马氏距离对比图表 ✓
                    """
                    result_label.setText(result_text.strip())
                    
                    print("=" * 60)
                    print("测试结果:")
                    print(f"分类结果: {classification}")
                    print(f"到正样本距离: {pos_dist}")
                    print(f"到负样本距离: {neg_dist}")
                    print("=" * 60)
                else:
                    result_label.setText("⚠️ 未获取到分析结果")
                    print("警告: 未获取到分析结果")
            else:
                result_label.setText("❌ 用户取消了操作")
                print("用户取消了操作")
                
        except Exception as e:
            error_msg = f"❌ 测试失败: {str(e)}"
            result_label.setText(error_msg)
            print(error_msg)
            print("详细错误信息:")
            import traceback
            traceback.print_exc()
    
    test_btn.clicked.connect(open_dialog)
    layout.addWidget(test_btn)
    layout.addWidget(result_label)
    
    layout.addStretch()
    
    # 显示主窗口
    main_window.show()
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    print("修复后的马氏距离分类子界面测试")
    print("=" * 60)
    print("本次修复内容:")
    print("1. 增加了详细的错误处理和调试信息")
    print("2. 修复了图表创建时的数据验证问题")
    print("3. 删除了多特征马氏距离分类分析图")
    print("4. 确保两个图表都能正确显示")
    print("5. 修复了马氏距离对比图表的数据验证")
    print("6. 调整了窗口大小(1000x700)适应两个图表")
    print("=" * 60)
    
    try:
        exit_code = test_fixed_mahalanobis_dialog()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

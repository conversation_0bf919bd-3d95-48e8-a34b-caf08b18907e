#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代工业监测仪表板设计测试
测试故障异常报警页面的现代化设计改进
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem

class ModernFaultAlarmTest(QMainWindow):
    """现代工业监测仪表板测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化测试界面"""
        self.setWindowTitle("现代工业设备监测仪表板 - 故障异常报警系统")
        self.setGeometry(100, 100, 1280, 900)  # 设置为目标分辨率
        
        # 设置现代化背景
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8fafc, stop: 1 #e2e8f0);
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        layout.addWidget(self.fault_alarm)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')  # 使用现代Fusion样式
    
    # 创建测试窗口
    window = ModernFaultAlarmTest()
    window.show()
    
    print("🎨 现代工业监测仪表板设计测试")
    print("=" * 50)
    print("✨ 设计特点:")
    print("  • 现代简约数据可视化风格")
    print("  • 平衡的颜色对比度")
    print("  • 易读的图表和字体层级")
    print("  • 浅蓝色背景")
    print("  • 圆角卡片UI元素")
    print("  • 微妙阴影和分隔线")
    print("  • 柔和渐变背景")
    print("  • 扁平风格图标")
    print("  • 统一的红橙色调警告配色")
    print("  • 扁平风格按钮")
    print("  • 渐变填充进度条")
    print("=" * 50)
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()

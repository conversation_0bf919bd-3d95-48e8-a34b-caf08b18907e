#!/usr/bin/env python3
"""
测试重构后的UI组件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from algorithms import create_classical_classifier, create_deep_learning_classifier

def test_classical_classifier():
    """测试传统分类器算法模块"""
    print("测试传统分类器算法模块...")
    
    try:
        classifier = create_classical_classifier()
        print(f"✓ 传统分类器创建成功: {type(classifier)}")
        
        # 测试是否有必要的方法
        methods = ['prepare_data', 'train', 'evaluate', 'predict', 'save_model', 'load_model']
        for method in methods:
            if hasattr(classifier, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                
    except Exception as e:
        print(f"✗ 传统分类器测试失败: {e}")

def test_deep_learning_classifier():
    """测试深度学习分类器算法模块"""
    print("\n测试深度学习分类器算法模块...")
    
    try:
        classifier = create_deep_learning_classifier()
        print(f"✓ 深度学习分类器创建成功: {type(classifier)}")
        
        # 测试是否有必要的方法
        methods = ['prepare_data', 'train', 'evaluate', 'predict', 'save_model', 'load_model', 'is_available']
        for method in methods:
            if hasattr(classifier, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                
        # 测试PyTorch可用性
        is_available = classifier.is_available()
        print(f"✓ PyTorch可用性: {is_available}")
                
    except Exception as e:
        print(f"✗ 深度学习分类器测试失败: {e}")

def test_ui_imports():
    """测试UI模块导入"""
    print("\n测试UI模块导入...")
    
    try:
        from ui.classical_classifier import ClassicalClassifier
        print("✓ 传统分类器UI导入成功")
    except Exception as e:
        print(f"✗ 传统分类器UI导入失败: {e}")
        
    try:
        from ui.deep_learning_classifier import DeepLearningClassifier
        print("✓ 深度学习分类器UI导入成功")
    except Exception as e:
        print(f"✗ 深度学习分类器UI导入失败: {e}")

if __name__ == "__main__":
    print("开始测试重构后的代码...")
    
    test_classical_classifier()
    test_deep_learning_classifier()
    test_ui_imports()
    
    print("\n测试完成！")

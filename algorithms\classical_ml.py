"""
传统机器学习算法模块
提供各种传统机器学习算法的统一接口
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
import joblib
import os
from typing import Dict, Any, Optional, Callable, Tuple, List


class ClassicalMLClassifier:
    """传统机器学习分类器"""
    
    def __init__(self):
        """初始化分类器"""
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_selector = None
        self.algorithm_configs = self._get_default_configs()
        self.training_history = []
        
    def _get_default_configs(self) -> Dict[str, Dict]:
        """获取默认算法配置"""
        return {
            'SVM': {
                'C': [0.1, 1, 10, 100],
                'kernel': ['linear', 'rbf', 'poly'],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
            },
            'Random Forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            },
            'KNN': {
                'n_neighbors': [3, 5, 7, 9, 11],
                'weights': ['uniform', 'distance'],
                'metric': ['euclidean', 'manhattan', 'minkowski']
            },
            'Naive Bayes': {
                'var_smoothing': [1e-9, 1e-8, 1e-7, 1e-6, 1e-5]
            },
            'Decision Tree': {
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'criterion': ['gini', 'entropy']
            }
        }
    
    def prepare_data(self, X: np.ndarray, y: np.ndarray, 
                    test_size: float = 0.2, 
                    random_state: int = 42,
                    scaling_method: str = 'standard',
                    feature_selection: Optional[str] = None,
                    n_features: int = 10) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            X: 特征数据
            y: 标签数据
            test_size: 测试集比例
            random_state: 随机种子
            scaling_method: 缩放方法 ('standard', 'minmax', 'none')
            feature_selection: 特征选择方法 ('kbest', 'rfe', None)
            n_features: 选择的特征数量
            
        Returns:
            X_train, X_test, y_train, y_test
        """
        # 标签编码
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=test_size, random_state=random_state, stratify=y_encoded
        )
        
        # 数据缩放
        if scaling_method == 'standard':
            self.scaler = StandardScaler()
            X_train = self.scaler.fit_transform(X_train)
            X_test = self.scaler.transform(X_test)
        elif scaling_method == 'minmax':
            self.scaler = MinMaxScaler()
            X_train = self.scaler.fit_transform(X_train)
            X_test = self.scaler.transform(X_test)
        
        # 特征选择
        if feature_selection == 'kbest':
            self.feature_selector = SelectKBest(f_classif, k=min(n_features, X_train.shape[1]))
            X_train = self.feature_selector.fit_transform(X_train, y_train)
            X_test = self.feature_selector.transform(X_test)
        elif feature_selection == 'rfe':
            # 使用随机森林作为基础估计器进行RFE
            base_estimator = RandomForestClassifier(n_estimators=50, random_state=random_state)
            self.feature_selector = RFE(base_estimator, n_features_to_select=min(n_features, X_train.shape[1]))
            X_train = self.feature_selector.fit_transform(X_train, y_train)
            X_test = self.feature_selector.transform(X_test)
        
        return X_train, X_test, y_train, y_test
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
             algorithm: str, config: Dict[str, Any],
             use_grid_search: bool = True,
             cv_folds: int = 5,
             progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X_train: 训练特征
            y_train: 训练标签
            algorithm: 算法名称
            config: 算法配置
            use_grid_search: 是否使用网格搜索
            cv_folds: 交叉验证折数
            progress_callback: 进度回调函数
            
        Returns:
            训练结果字典
        """
        if progress_callback:
            progress_callback(10, "初始化模型...")
        
        # 创建基础模型
        base_model = self._create_base_model(algorithm)
        
        if use_grid_search and algorithm in self.algorithm_configs:
            if progress_callback:
                progress_callback(20, "开始网格搜索...")
            
            # 网格搜索
            param_grid = config if config else self.algorithm_configs[algorithm]
            grid_search = GridSearchCV(
                base_model, param_grid, cv=cv_folds, 
                scoring='accuracy', n_jobs=-1, verbose=0
            )
            
            grid_search.fit(X_train, y_train)
            self.model = grid_search.best_estimator_
            best_params = grid_search.best_params_
            best_score = grid_search.best_score_
            
            if progress_callback:
                progress_callback(80, "网格搜索完成...")
        else:
            if progress_callback:
                progress_callback(20, "训练模型...")
            
            # 直接训练
            if config:
                base_model.set_params(**config)
            self.model = base_model
            self.model.fit(X_train, y_train)
            
            # 交叉验证评估
            cv_scores = cross_val_score(self.model, X_train, y_train, cv=cv_folds)
            best_params = config if config else {}
            best_score = cv_scores.mean()
        
        if progress_callback:
            progress_callback(100, "训练完成!")
        
        # 记录训练历史
        training_record = {
            'algorithm': algorithm,
            'best_params': best_params,
            'best_score': best_score,
            'training_samples': len(X_train),
            'features': X_train.shape[1]
        }
        self.training_history.append(training_record)
        
        return training_record
    
    def _create_base_model(self, algorithm: str):
        """创建基础模型"""
        if algorithm == 'SVM':
            return SVC(random_state=42)
        elif algorithm == 'Random Forest':
            return RandomForestClassifier(random_state=42)
        elif algorithm == 'KNN':
            return KNeighborsClassifier()
        elif algorithm == 'Naive Bayes':
            return GaussianNB()
        elif algorithm == 'Decision Tree':
            return DecisionTreeClassifier(random_state=42)
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """
        评估模型
        
        Args:
            X_test: 测试特征
            y_test: 测试标签
            
        Returns:
            评估结果字典
        """
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        # 预测
        y_pred = self.model.predict(X_test)
        y_pred_proba = None
        if hasattr(self.model, 'predict_proba'):
            y_pred_proba = self.model.predict_proba(X_test)
        
        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, average='weighted')
        recall = recall_score(y_test, y_pred, average='weighted')
        f1 = f1_score(y_test, y_pred, average='weighted')
        cm = confusion_matrix(y_test, y_pred)
        
        # 分类报告
        class_names = self.label_encoder.classes_ if self.label_encoder else None
        report = classification_report(y_test, y_pred, target_names=class_names, output_dict=True)
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': cm,
            'classification_report': report,
            'y_test': y_test,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'class_names': class_names
        }
    
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        预测新数据
        
        Args:
            X: 特征数据
            
        Returns:
            预测标签和概率
        """
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        # 数据预处理
        if self.scaler:
            X = self.scaler.transform(X)
        if self.feature_selector:
            X = self.feature_selector.transform(X)
        
        # 预测
        y_pred = self.model.predict(X)
        y_pred_proba = None
        if hasattr(self.model, 'predict_proba'):
            y_pred_proba = self.model.predict_proba(X)
        
        # 标签解码
        if self.label_encoder:
            y_pred = self.label_encoder.inverse_transform(y_pred)
        
        return y_pred, y_pred_proba
    
    def get_feature_importance(self) -> Optional[np.ndarray]:
        """获取特征重要性"""
        if self.model is None:
            return None
        
        if hasattr(self.model, 'feature_importances_'):
            return self.model.feature_importances_
        elif hasattr(self.model, 'coef_'):
            return np.abs(self.model.coef_[0])
        else:
            return None
    
    def save_model(self, filepath: str) -> None:
        """
        保存模型
        
        Args:
            filepath: 保存路径
        """
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'feature_selector': self.feature_selector,
            'training_history': self.training_history
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        joblib.dump(model_data, filepath)
    
    def load_model(self, filepath: str) -> None:
        """
        加载模型
        
        Args:
            filepath: 模型路径
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"模型文件不存在: {filepath}")
        
        model_data = joblib.load(filepath)
        self.model = model_data['model']
        self.scaler = model_data.get('scaler')
        self.label_encoder = model_data.get('label_encoder')
        self.feature_selector = model_data.get('feature_selector')
        self.training_history = model_data.get('training_history', [])
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model is None:
            return {}
        
        info = {
            'algorithm': type(self.model).__name__,
            'parameters': self.model.get_params(),
            'training_history': self.training_history
        }
        
        if hasattr(self.model, 'n_features_in_'):
            info['n_features'] = self.model.n_features_in_
        
        return info

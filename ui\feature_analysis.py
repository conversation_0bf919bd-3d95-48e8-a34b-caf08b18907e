"""
特征提取与分析模块
基于date1/tdms_analyzer.py，实现PyQt5版本的特征提取与分析功能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QTextEdit, QScrollArea, QFrame, QSplitter,
    QGroupBox, QProgressBar, QMessageBox, QFileDialog,
    QTabWidget, QSpinBox, QDoubleSpinBox, QDialog
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QCursor, QPainter

# 科学计算库
from nptdms import TdmsFile
from scipy import signal
from scipy.fft import fft, fftfreq, ifft
from scipy.stats import kurtosis, skew
import pywt
from scipy.signal import hilbert

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, HIGHLIGHT_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, WARNING_COLOR,
    ERROR_COLOR, INFO_COLOR, CHART_COLORS
)
# 使用新的算法接口
from algorithms import get_algorithm_interface


class FeatureMethodCard(QFrame):
    """特征方法选择卡片组件"""
    clicked = pyqtSignal(int)  # 发送卡片索引

    def __init__(self, method_info, index):
        super().__init__()
        self.method_info = method_info
        self.index = index
        self.selected = False
        self.init_ui()

    def init_ui(self):
        """初始化卡片界面"""
        self.setFixedSize(90, 90)  # 减小卡片大小
        self.setCursor(QCursor(Qt.PointingHandCursor))

        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)  # 减小边距
        layout.setSpacing(3)  # 减小间距
        layout.setAlignment(Qt.AlignCenter)

        # 图标标签
        self.icon_label = QLabel(self.method_info["icon"])
        self.icon_label.setAlignment(Qt.AlignCenter)
        self.icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: #ffffff;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(self.icon_label)

        # 方法名称标签
        self.name_label = QLabel(self.method_info["short_name"])
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setStyleSheet(f"""
            QLabel {{
                font-size: 11px;
                color: {TEXT_PRIMARY};
                background: transparent;
                border: none;
                font-weight: bold;
                line-height: 1.1;
            }}
        """)
        layout.addWidget(self.name_label)

        self.update_style()

    def update_style(self):
        """更新卡片样式"""
        if self.selected:
            # 选中状态样式
            self.setStyleSheet(f"""
                FeatureMethodCard {{
                    background-color: {ACCENT_COLOR};
                    border: 2px solid {HIGHLIGHT_COLOR};
                    border-radius: 12px;
                }}
                FeatureMethodCard:hover {{
                    background-color: #7d6ef0;
                    border: 2px solid {HIGHLIGHT_COLOR};
                }}
            """)
        else:
            # 未选中状态样式
            self.setStyleSheet(f"""
                FeatureMethodCard {{
                    background-color: {SECONDARY_BG};
                    border: 2px solid #3d3d5c;
                    border-radius: 12px;
                }}
                FeatureMethodCard:hover {{
                    background-color: #353545;
                    border: 2px solid #4d4d6c;
                }}
            """)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.selected = not self.selected
            self.update_style()
            self.clicked.emit(self.index)
        super().mousePressEvent(event)

    def set_selected(self, selected):
        """设置选中状态"""
        self.selected = selected
        self.update_style()

    def is_selected(self):
        """获取选中状态"""
        return self.selected


class AnalysisWorker(QThread):
    """分析工作线程"""
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态信息
    analysis_completed = pyqtSignal(list)  # 分析结果
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self, data, sample_rate, selected_methods, analysis_methods):
        super().__init__()
        self.data = data
        self.sample_rate = sample_rate
        self.selected_methods = selected_methods
        self.analysis_methods = analysis_methods
        self.results = []
        self.algorithm_interface = get_algorithm_interface(sample_rate)
        
    def run(self):
        """执行分析"""
        try:
            self.results = []
            total_methods = len(self.selected_methods)
            
            for i, method_idx in enumerate(self.selected_methods):
                method_name = self.analysis_methods[method_idx]["name"]
                self.progress_updated.emit(
                    int((i / total_methods) * 100),
                    f"正在分析: {method_name}"
                )
                
                # 创建图形，增加高度以容纳更多内容
                fig = Figure(figsize=(12, 8), facecolor=PRIMARY_BG)
                ax = fig.add_subplot(111)
                
                # 设置图表样式
                ax.set_facecolor(SECONDARY_BG)
                ax.tick_params(colors=TEXT_PRIMARY)
                ax.xaxis.label.set_color(TEXT_PRIMARY)
                ax.yaxis.label.set_color(TEXT_PRIMARY)
                ax.title.set_color(TEXT_PRIMARY)
                ax.grid(True, alpha=0.3, color=TEXT_SECONDARY)
                
                try:
                    # 使用新的算法接口执行分析
                    self.algorithm_interface.analyze_signal(self.data, method_idx, ax)

                    # 调整布局，增加更多边距以防止标签被截断
                    fig.tight_layout(pad=4.0, rect=[0, 0.15, 1, 0.92])
                    
                except Exception as e:
                    ax.text(0.5, 0.5, f'分析失败:\n{str(e)}',
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=16, color=ERROR_COLOR)
                
                self.results.append((method_name, fig))

            self.progress_updated.emit(100, "分析完成")
            self.analysis_completed.emit(self.results)

            # 调试信息
            print(f"分析完成，总结果数量: {len(self.results)}")
            
        except Exception as e:
            self.error_occurred.emit(f"分析过程中出现错误: {str(e)}")


class FeatureAnalysis(QWidget):
    """特征提取与分析主界面"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.current_file_path = None
        self.current_file_info = None
        self.current_data = None
        self.current_channels = []
        self.selected_channel = None
        self.sample_rate = 1000
        self.analysis_worker = None
        self.analysis_results = []
        self.extracted_features = {}  # 存储提取的数值特征
        self.selected_method_indices = []  # 存储选中的方法索引
        self.selected_methods = []  # 存储选中的方法名称
        
        # 13种分析方法及其图标映射
        self.analysis_methods = [
            {"name": "时域有量纲特征值", "icon": "📊", "short_name": "有量纲\n特征"},
            {"name": "时域无量纲特征值", "icon": "📈", "short_name": "无量纲\n特征"},
            {"name": "自相关函数", "icon": "🔄", "short_name": "自相关\n函数"},
            {"name": "互相关函数", "icon": "↔️", "short_name": "互相关\n函数"},
            {"name": "频谱分析", "icon": "📊", "short_name": "频谱\n(FFT)"},
            {"name": "倒频谱", "icon": "🔄", "short_name": "倒频谱"},
            {"name": "包络谱", "icon": "📦", "short_name": "包络谱"},
            {"name": "阶比谱", "icon": "📏", "short_name": "阶比谱"},
            {"name": "功率谱", "icon": "⚡", "short_name": "功率谱"},
            {"name": "短时傅里叶变换", "icon": "🔍", "short_name": "STFT"},
            {"name": "魏格纳威尔分布", "icon": "🌊", "short_name": "WVD"},
            {"name": "小波变换", "icon": "〰️", "short_name": "小波变换"},
            {"name": "本征模函数", "icon": "🌀", "short_name": "EMD"}
        ]
        
        self.init_ui()

    def get_group_box_style(self):
        """获取统一的分组框样式"""
        return f"""
            QGroupBox {{
                font-size: 15px;
                font-weight: 600;
                color: {TEXT_PRIMARY};
                border: 1px solid #e0e6ed;
                border-radius: 12px;
                margin-top: 8px;
                padding-top: 15px;
                background-color: white;
                font-family: 'Microsoft YaHei';
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {ACCENT_COLOR};
                font-weight: 600;
                background-color: white;
            }}
        """

    def get_primary_button_style(self):
        """获取主要按钮样式"""
        return f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-size: 14px;
                font-weight: 500;
                min-height: 40px;
                padding: 10px 15px;
                border-radius: 10px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #0052a3;
            }}
            QPushButton:pressed {{
                background-color: #004080;
            }}
            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #999999;
            }}
        """

    def get_secondary_button_style(self):
        """获取次要按钮样式"""
        return f"""
            QPushButton {{
                background-color: #f8f9fa;
                color: {TEXT_PRIMARY};
                font-size: 13px;
                font-weight: 500;
                min-height: 36px;
                padding: 8px 16px;
                border-radius: 8px;
                border: 1px solid #e0e6ed;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #e9ecef;
                border-color: #adb5bd;
            }}
            QPushButton:pressed {{
                background-color: #dee2e6;
                border-color: #6c757d;
            }}
        """

    def get_info_label_style(self):
        """获取信息标签样式"""
        return f"""
            QLabel {{
                color: {TEXT_SECONDARY};
                font-size: 13px;
                padding: 12px 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                min-height: 45px;
                font-family: 'Microsoft YaHei';
                line-height: 1.4;
            }}
        """

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("特征提取与分析")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: 600;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
                font-family: 'Microsoft YaHei';
            }}
        """)
        layout.addWidget(title_label)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setHandleWidth(6)
        main_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {SECONDARY_BG};
                border-radius: 3px;
            }}
            QSplitter::handle:hover {{
                background-color: {ACCENT_COLOR};
            }}
        """)
        layout.addWidget(main_splitter)

        # 左侧控制面板
        self.create_control_panel(main_splitter)

        # 右侧结果显示区域
        self.create_result_panel(main_splitter)

        # 设置分割器比例 - 优化为1280*900分辨率
        main_splitter.setSizes([350, 930])

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {SECONDARY_BG};
                border-radius: 10px;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                background-color: {SECONDARY_BG};
                min-height: 30px;
            }}
            QProgressBar::chunk {{
                background-color: {ACCENT_COLOR};
                border-radius: 8px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("请选择TDMS文件开始分析")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_SECONDARY};
                font-size: 18px;
                font-weight: 500;
                padding: 10px;
                font-family: 'Microsoft YaHei';
            }}
        """)
        layout.addWidget(self.status_label)

    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_widget.setStyleSheet(f"background-color: {PRIMARY_BG};")
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(15, 15, 15, 15)
        control_layout.setSpacing(20)

        # 文件选择区域
        file_group = QGroupBox("")
        file_group.setStyleSheet(self.get_group_box_style())
        file_layout = QVBoxLayout(file_group)
        file_layout.setContentsMargins(15, 20, 15, 15)
        file_layout.setSpacing(12)

        # 选择文件按钮
        self.select_file_btn = QPushButton("选择TDMS文件")
        self.select_file_btn.clicked.connect(self.select_tdms_file)
        self.select_file_btn.setStyleSheet(self.get_primary_button_style())
        file_layout.addWidget(self.select_file_btn)

        control_layout.addWidget(file_group)

        # 分析方法选择区域
        method_group = QGroupBox("")
        method_group.setStyleSheet(self.get_group_box_style())
        method_layout = QVBoxLayout(method_group)
        method_layout.setContentsMargins(15, 20, 15, 15)
        method_layout.setSpacing(12)

        # 选择方法按钮
        self.select_methods_btn = QPushButton("选择特征提取方法")
        self.select_methods_btn.clicked.connect(self.open_config_dialog)
        self.select_methods_btn.setStyleSheet(self.get_primary_button_style())
        method_layout.addWidget(self.select_methods_btn)

        # 已选择方法显示区域
        self.selected_methods_label = QLabel("未选择任何方法")
        self.selected_methods_label.setWordWrap(True)
        self.selected_methods_label.setStyleSheet(self.get_info_label_style())
        method_layout.addWidget(self.selected_methods_label)

        # 清除选择按钮
        clear_selection_btn = QPushButton("清除选择")
        clear_selection_btn.clicked.connect(self.clear_method_selection)
        clear_selection_btn.setStyleSheet(self.get_secondary_button_style())
        method_layout.addWidget(clear_selection_btn)

        control_layout.addWidget(method_group)

        # 操作按钮区域
        button_group = QGroupBox("")
        button_group.setStyleSheet(self.get_group_box_style())
        button_layout = QVBoxLayout(button_group)
        button_layout.setContentsMargins(15, 20, 15, 15)
        button_layout.setSpacing(12)

        # 分析按钮
        self.analyze_btn = QPushButton("开始分析")
        self.analyze_btn.clicked.connect(self.start_analysis)
        self.analyze_btn.setEnabled(False)
        self.analyze_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: white;
                font-size: 14px;
                font-weight: 500;
                min-height: 42px;
                padding: 12px 24px;
                border-radius: 10px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #00c9a7;
            }}
            QPushButton:pressed {{
                background-color: #00a085;
            }}
            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #999999;
            }}
        """)
        button_layout.addWidget(self.analyze_btn)

        # 故障判断按钮
        self.fault_diagnosis_btn = QPushButton("故障判断")
        self.fault_diagnosis_btn.clicked.connect(self.open_fault_diagnosis)
        self.fault_diagnosis_btn.setEnabled(False)
        self.fault_diagnosis_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                font-size: 14px;
                font-weight: 500;
                min-height: 42px;
                padding: 12px 24px;
                border-radius: 10px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #5a9cff;
            }}
            QPushButton:pressed {{
                background-color: #4a8cef;
            }}
            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #999999;
            }}
        """)
        button_layout.addWidget(self.fault_diagnosis_btn)

        control_layout.addWidget(button_group)
        control_layout.addStretch()
        parent.addWidget(control_widget)

    def create_result_panel(self, parent):
        """创建右侧结果显示面板"""
        result_widget = QWidget()
        result_widget.setStyleSheet(f"background-color: {PRIMARY_BG};")
        result_layout = QVBoxLayout(result_widget)
        result_layout.setContentsMargins(15, 15, 15, 15)
        result_layout.setSpacing(15)

        # 结果区域标题和控制按钮
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 12px;
                border: 1px solid #e0e6ed;
                padding: 10px;
            }}
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 12, 15, 12)
        header_layout.setSpacing(15)

        result_title = QLabel("📊 分析结果")
        result_title.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                font-family: 'Microsoft YaHei';
            }}
        """)
        header_layout.addWidget(result_title)

        header_layout.addStretch()

        # 保存和清除按钮
        self.save_btn = QPushButton("💾 保存")
        self.save_btn.clicked.connect(self.save_results)
        self.save_btn.setEnabled(False)
        self.save_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: white;
                font-size: 13px;
                font-weight: 500;
                min-height: 36px;
                padding: 8px 16px;
                border-radius: 8px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #00c9a7;
            }}
            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #999999;
            }}
        """)

        self.clear_btn = QPushButton("🗑️ 清除")
        self.clear_btn.clicked.connect(self.clear_results)
        self.clear_btn.setEnabled(False)
        self.clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #f8f9fa;
                color: {TEXT_PRIMARY};
                font-size: 13px;
                font-weight: 500;
                min-height: 36px;
                padding: 8px 16px;
                border-radius: 8px;
                border: 1px solid #e0e6ed;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #e9ecef;
                border-color: #adb5bd;
            }}
            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #999999;
                border-color: #e0e6ed;
            }}
        """)

        header_layout.addWidget(self.save_btn)
        header_layout.addWidget(self.clear_btn)

        result_layout.addWidget(header_frame)

        # 结果显示滚动区域
        self.result_scroll = QScrollArea()
        self.result_scroll.setWidgetResizable(True)
        self.result_scroll.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid #e0e6ed;
                border-radius: 12px;
                background-color: white;
            }}
            QScrollBar:vertical {{
                background-color: #f8f9fa;
                width: 10px;
                border-radius: 5px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background-color: #adb5bd;
                border-radius: 5px;
                min-height: 20px;
                margin: 2px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {ACCENT_COLOR};
            }}
        """)

        self.result_content = QWidget()
        self.result_content.setStyleSheet("background-color: white;")
        self.result_layout = QVBoxLayout(self.result_content)
        self.result_layout.setAlignment(Qt.AlignTop)
        self.result_layout.setContentsMargins(20, 20, 20, 20)
        self.result_layout.setSpacing(15)

        # 默认提示信息
        self.default_label = QLabel("📋 请选择TDMS文件和分析方法，然后点击'开始分析'")
        self.default_label.setAlignment(Qt.AlignCenter)
        self.default_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_SECONDARY};
                font-size: 16px;
                font-weight: 500;
                padding: 60px 30px;
                background-color: {SECONDARY_BG};
                border-radius: 12px;
                border: 2px dashed {ACCENT_COLOR};
                font-family: 'Microsoft YaHei';
                line-height: 1.4;
            }}
        """)
        self.result_layout.addWidget(self.default_label)

        self.result_scroll.setWidget(self.result_content)
        result_layout.addWidget(self.result_scroll)

        parent.addWidget(result_widget)

    def select_tdms_file(self):
        """选择TDMS文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择TDMS文件", "", "TDMS files (*.tdms);;All files (*.*)"
        )

        if file_path:
            self.load_tdms_file(file_path)

    def load_tdms_file(self, file_path):
        """加载TDMS文件"""
        try:
            self.status_label.setText("正在加载TDMS文件...")
            self.status_label.setStyleSheet(f"color: {INFO_COLOR}; font-size: 16px;")

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            # 读取TDMS文件
            tdms_data = TdmsFile.read(file_path)

            # 获取所有通道
            channels = []
            for group in tdms_data.groups():
                for channel in group.channels():
                    channels.append(channel.name)

            self.progress_bar.setVisible(False)

            if not channels:
                QMessageBox.warning(self, "警告", "TDMS文件中未找到有效通道")
                self.status_label.setText("文件中无有效通道")
                self.status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 16px;")
                return

            # 尝试从TDMS文件中获取采样率
            detected_sample_rate = self.detect_sample_rate(tdms_data)
            if detected_sample_rate:
                self.sample_rate = detected_sample_rate

            # 更新界面
            self.current_file_path = file_path
            self.current_data = tdms_data
            self.current_channels = channels



            self.status_label.setText(f"成功加载TDMS文件，发现 {len(channels)} 个通道")
            self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 16px;")

        except FileNotFoundError:
            self.progress_bar.setVisible(False)
            error_msg = f"文件不存在: {file_path}"
            QMessageBox.critical(self, "文件错误", error_msg)
            self.status_label.setText("文件不存在")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
        except PermissionError:
            self.progress_bar.setVisible(False)
            error_msg = f"没有权限访问文件: {file_path}"
            QMessageBox.critical(self, "权限错误", error_msg)
            self.status_label.setText("文件访问权限不足")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
        except Exception as e:
            self.progress_bar.setVisible(False)
            error_msg = f"加载TDMS文件失败:\n{str(e)}"
            QMessageBox.critical(self, "加载错误", error_msg)
            self.status_label.setText("加载文件失败")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")

    def detect_sample_rate(self, tdms_data):
        """从TDMS文件中检测采样率"""
        try:
            # 尝试从文件属性中获取采样率
            for group in tdms_data.groups():
                for channel in group.channels():
                    # 检查通道属性中的采样率信息
                    if hasattr(channel, 'properties'):
                        props = channel.properties
                        # 常见的采样率属性名
                        sample_rate_keys = ['wf_increment', 'wf_start_time', 'sampling_rate', 'sample_rate']
                        for key in sample_rate_keys:
                            if key in props:
                                if key == 'wf_increment':
                                    # wf_increment是时间间隔，采样率是其倒数
                                    increment = props[key]
                                    if increment > 0:
                                        return 1.0 / increment
                                else:
                                    return float(props[key])

                    # 如果没有找到属性，尝试从数据长度和时间范围估算
                    try:
                        data = channel[:]
                        if len(data) > 1:
                            # 假设数据是等间隔采样的，尝试估算采样率
                            # 这是一个简单的估算，可能不够准确
                            if hasattr(channel, 'time_track'):
                                time_data = channel.time_track()
                                if len(time_data) > 1:
                                    time_diff = time_data[-1] - time_data[0]
                                    if time_diff > 0:
                                        return (len(data) - 1) / time_diff.total_seconds()
                    except:
                        continue

            # 如果都没有找到，返回None，使用默认值
            return None

        except Exception as e:
            print(f"检测采样率时出错: {e}")
            return None









    def update_analyze_button_state(self):
        """更新分析按钮状态"""
        # 只有在配置完成后才启用分析按钮
        has_file = self.current_file_path is not None
        has_config = (self.selected_channel is not None and
                     len(self.selected_methods) > 0)

        can_analyze = has_file and has_config

        if hasattr(self, 'analyze_btn') and self.analyze_btn is not None:
            self.analyze_btn.setEnabled(can_analyze)



    def wheelEvent(self, event):
        """处理鼠标滚轮事件"""
        # 检查是否在方法选择区域内
        if hasattr(self, 'method_scroll_area'):
            scroll_bar = self.method_scroll_area.verticalScrollBar()
            if scroll_bar and scroll_bar.isVisible():
                # 计算滚动步长
                delta = event.angleDelta().y()
                step = scroll_bar.singleStep() * 3  # 增加滚动速度

                if delta > 0:
                    # 向上滚动
                    scroll_bar.setValue(scroll_bar.value() - step)
                else:
                    # 向下滚动
                    scroll_bar.setValue(scroll_bar.value() + step)

                event.accept()
                return

        # 如果不在方法选择区域，使用默认处理
        super().wheelEvent(event)



    def clear_method_selection(self):
        """清除分析方法选择"""
        self.selected_methods = []
        self.selected_channel = None
        self.update_method_display()
        self.update_analyze_button_state()

    def start_analysis(self):
        """开始分析"""
        # 验证前置条件
        if not self.current_file_path or not self.selected_channel:
            QMessageBox.warning(self, "警告", "请先选择TDMS文件和配置分析参数")
            self.status_label.setText("请先选择TDMS文件和配置分析参数")
            self.status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 16px;")
            return

        # 获取选中的分析方法
        if not self.selected_methods:
            QMessageBox.warning(self, "警告", "请选择至少一种分析方法")
            self.status_label.setText("请选择至少一种分析方法")
            self.status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 16px;")
            return

        # 将方法名称转换为索引
        method_name_to_index = {
            "时域有量纲特征值": 0,
            "时域无量纲特征值": 1,
            "自相关函数": 2,
            "互相关函数": 3,
            "频谱分析": 4,
            "倒频谱": 5,
            "包络谱": 6,
            "阶比谱": 7,
            "功率谱": 8,
            "短时傅里叶变换": 9,
            "魏格纳威尔分布": 10,
            "小波变换": 11,
            "本征模函数": 12
        }

        selected_methods = []
        for method_name in self.selected_methods:
            if method_name in method_name_to_index:
                selected_methods.append(method_name_to_index[method_name])

        # 验证采样率
        if self.sample_rate <= 0:
            QMessageBox.critical(self, "错误", "采样率必须大于0")
            self.status_label.setText("采样率配置错误")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
            return

        # 获取通道数据
        try:
            # 重新读取TDMS文件
            from nptdms import TdmsFile
            with TdmsFile.read(self.current_file_path) as tdms_file:
                # 解析通道名称
                if '/' in self.selected_channel:
                    group_name, channel_name = self.selected_channel.split('/', 1)
                    channel = tdms_file[group_name][channel_name]
                else:
                    # 如果没有组名，尝试在所有组中查找
                    channel = None
                    for group in tdms_file.groups():
                        for ch in group.channels():
                            if ch.name == self.selected_channel:
                                channel = ch
                                break
                        if channel:
                            break

                if channel is None:
                    QMessageBox.critical(self, "错误", f"找不到通道: {self.selected_channel}")
                    return

                data = channel[:]

            if data is None:
                QMessageBox.critical(self, "错误", "无法获取通道数据")
                self.status_label.setText("无法获取通道数据")
                self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
                return

            # 验证数据有效性
            if len(data) == 0:
                QMessageBox.critical(self, "错误", "通道数据为空")
                self.status_label.setText("通道数据为空")
                self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
                return

            if np.all(np.isnan(data)):
                QMessageBox.critical(self, "错误", "通道数据全部为NaN")
                self.status_label.setText("通道数据无效")
                self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
                return

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取通道数据失败:\n{str(e)}")
            self.status_label.setText("获取通道数据失败")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
            return

        # 禁用分析按钮，显示进度条
        self.analyze_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 更新状态
        self.status_label.setText(f"准备分析 {len(selected_methods)} 种方法...")
        self.status_label.setStyleSheet(f"color: {INFO_COLOR}; font-size: 16px;")

        # 创建并启动分析线程
        self.analysis_worker = AnalysisWorker(
            data, self.sample_rate, selected_methods, self.analysis_methods
        )
        self.analysis_worker.progress_updated.connect(self.on_progress_updated)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_worker.error_occurred.connect(self.on_analysis_error)
        self.analysis_worker.start()

    def get_channel_data(self, channel_name):
        """获取指定通道的数据"""
        if not self.current_data:
            return None

        for group in self.current_data.groups():
            for channel in group.channels():
                if channel.name == channel_name:
                    return channel[:]
        return None

    def on_progress_updated(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"color: {INFO_COLOR}; font-size: 16px;")

    def on_analysis_completed(self, results):
        """分析完成"""
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)
        self.analysis_results = results

        if not results:
            self.status_label.setText("分析完成，但未生成任何结果")
            self.status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 16px;")
            QMessageBox.warning(self, "警告", "分析完成，但未生成任何有效结果")
            return

        # 显示结果
        try:
            self.display_results(results)

            # 提取数值特征用于故障判断
            self.extract_features_for_diagnosis()

            # 启用保存和清除按钮
            self.save_btn.setEnabled(True)
            self.clear_btn.setEnabled(True)

            # 启用故障判断按钮（如果有特征数据）
            if self.extracted_features:
                self.fault_diagnosis_btn.setEnabled(True)

            self.status_label.setText(f"分析完成，共生成 {len(results)} 个结果，提取 {len(self.extracted_features)} 个特征")
            self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 16px;")

            # 显示成功消息
            QMessageBox.information(self, "分析完成",
                                  f"成功完成 {len(results)} 种分析方法\n"
                                  f"结果已显示在右侧面板中\n"
                                  f"提取了 {len(self.extracted_features)} 个数值特征")

        except Exception as e:
            self.status_label.setText("显示结果时出错")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
            QMessageBox.critical(self, "显示错误", f"显示分析结果时出错:\n{str(e)}")

    def on_analysis_error(self, error_message):
        """分析出错"""
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)

        self.status_label.setText("分析失败")
        self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")

        # 显示详细错误信息
        QMessageBox.critical(self, "分析错误",
                           f"分析过程中出现错误:\n\n{error_message}\n\n"
                           f"请检查:\n"
                           f"1. TDMS文件是否完整\n"
                           f"2. 通道数据是否有效\n"
                           f"3. 采样率设置是否正确")

    def display_results(self, results):
        """显示分析结果"""
        # 清除之前的结果显示（但不清除数据）
        self.clear_result_display()

        for method_name, fig in results:
            # 创建图表容器
            canvas = FigureCanvas(fig)
            canvas.setMinimumHeight(500)  # 增加最小高度以容纳更多内容

            # 创建容器来包含图表和标题
            container = QWidget()
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(10, 10, 10, 15)  # 增加底部边距

            # 先添加图表
            container_layout.addWidget(canvas)

            # 添加方法名称标签在图表下方
            method_label = QLabel(method_name)
            method_label.setAlignment(Qt.AlignCenter)
            method_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 18px;
                    font-weight: bold;
                    color: {ACCENT_COLOR};
                    padding: 10px;
                    margin-top: 5px;
                }}
            """)

            container_layout.addWidget(method_label)

            # 将整个容器添加到结果布局
            self.result_layout.addWidget(container)

            # 添加分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setStyleSheet(f"color: {SECONDARY_BG};")
            self.result_layout.addWidget(separator)

    def save_results(self):
        """保存分析结果"""
        # 检查是否有分析结果
        if not hasattr(self, 'analysis_results') or not self.analysis_results:
            QMessageBox.warning(self, "警告", "没有可保存的分析结果，请先进行分析")
            return

        # 创建保存文件夹
        save_dir = "analysis_results"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 生成文件名
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 如果有当前文件信息，包含在文件名中
        file_prefix = "tdms_analysis"
        if self.current_file_info:
            vehicle = self.current_file_info.get('车型', '').replace(' ', '_')
            component = self.current_file_info.get('部件', '').replace(' ', '_')
            if vehicle and component:
                file_prefix = f"{vehicle}_{component}_analysis"

        default_name = f"{file_prefix}_{timestamp}.png"
        default_path = os.path.join(save_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存分析结果", default_path,
            "PNG files (*.png);;PDF files (*.pdf);;All files (*.*)"
        )

        if file_path:
            try:
                self.status_label.setText("正在保存分析结果...")
                self.status_label.setStyleSheet(f"color: {INFO_COLOR}; font-size: 16px;")

                # 显示进度条
                self.progress_bar.setVisible(True)
                self.progress_bar.setRange(0, 0)

                # 确保目录存在
                save_dir = os.path.dirname(file_path)
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)

                # 合并所有图表保存
                if len(self.analysis_results) == 1:
                    # 只有一个图表，直接保存
                    fig = self.analysis_results[0][1]
                    # 增加边距，确保标题不被裁剪
                    fig.savefig(file_path, dpi=300, bbox_inches='tight',
                               facecolor=PRIMARY_BG, edgecolor='none', pad_inches=0.5)
                else:
                    # 多个图表，创建合并图像
                    self.save_combined_results(file_path)

                self.progress_bar.setVisible(False)
                self.status_label.setText("分析结果保存成功")
                self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 16px;")

                QMessageBox.information(self, "保存成功",
                                      f"分析结果已保存到:\n{file_path}\n\n"
                                      f"共保存 {len(self.analysis_results)} 个分析结果")

            except Exception as e:
                self.progress_bar.setVisible(False)
                self.status_label.setText("保存失败")
                self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 16px;")
                QMessageBox.critical(self, "保存错误", f"保存失败:\n{str(e)}")

    def save_combined_results(self, file_path):
        """保存合并的分析结果"""
        import matplotlib.pyplot as plt

        # 计算合并图像的尺寸
        num_results = len(self.analysis_results)
        cols = 2 if num_results > 1 else 1
        rows = (num_results + cols - 1) // cols

        # 创建合并图像，增加高度以容纳底部标题
        fig_width = 20 if cols == 2 else 12
        fig_height = 7 * rows  # 增加高度

        combined_fig = plt.figure(figsize=(fig_width, fig_height), facecolor=PRIMARY_BG)

        for i, (method_name, original_fig) in enumerate(self.analysis_results):
            # 创建子图，为底部标题留出空间
            ax = combined_fig.add_subplot(rows, cols, i + 1)

            # 复制原图的内容到新的子图
            original_ax = original_fig.axes[0]

            # 复制线条数据
            for line in original_ax.get_lines():
                ax.plot(line.get_xdata(), line.get_ydata(),
                       color=line.get_color(), linewidth=line.get_linewidth())

            # 复制柱状图数据
            for patch in original_ax.patches:
                if hasattr(patch, 'get_height'):  # 柱状图
                    ax.bar(patch.get_x(), patch.get_height(),
                          width=patch.get_width(), color=patch.get_facecolor(),
                          alpha=patch.get_alpha())

            # 复制图像数据（如时频图）
            for image in original_ax.images:
                ax.imshow(image.get_array(), aspect='auto',
                         extent=image.get_extent(), cmap=image.get_cmap())

            # 设置标签（不设置标题，标题放在底部）
            ax.set_xlabel(original_ax.get_xlabel(), fontsize=12, color=TEXT_PRIMARY)
            ax.set_ylabel(original_ax.get_ylabel(), fontsize=12, color=TEXT_PRIMARY)

            # 设置样式
            ax.set_facecolor(SECONDARY_BG)
            ax.tick_params(colors=TEXT_PRIMARY, labelsize=8)
            ax.grid(True, alpha=0.3, color=TEXT_SECONDARY)

            # 在子图下方添加标题，增加更多间距
            ax.text(0.5, -0.35, method_name, ha='center', va='top',
                   transform=ax.transAxes, fontsize=14, fontweight='bold',
                   color=TEXT_PRIMARY)

        # 调整布局并保存，增加更多底部边距以避免标题被截断
        combined_fig.tight_layout(pad=4.0, rect=[0, 0.12, 1, 0.98])
        combined_fig.savefig(file_path, dpi=300, bbox_inches='tight',
                           facecolor=PRIMARY_BG, edgecolor='none', pad_inches=0.8)
        plt.close(combined_fig)

    def clear_result_display(self):
        """只清除结果显示，不清除数据"""
        # 清除结果布局中的所有控件
        while self.result_layout.count():
            child = self.result_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def clear_results(self):
        """清除分析结果"""
        # 清除显示
        self.clear_result_display()

        # 重新添加默认提示
        self.default_label = QLabel("📋 请选择TDMS文件和分析方法，然后点击'开始分析'")
        self.default_label.setAlignment(Qt.AlignCenter)
        self.default_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_SECONDARY};
                font-size: 16px;
                font-weight: 500;
                padding: 60px 30px;
                background-color: {SECONDARY_BG};
                border-radius: 12px;
                border: 2px dashed {ACCENT_COLOR};
                font-family: 'Microsoft YaHei';
                line-height: 1.4;
            }}
        """)
        self.result_layout.addWidget(self.default_label)

        # 禁用保存和清除按钮
        self.save_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        self.fault_diagnosis_btn.setEnabled(False)

        # 清除结果数据
        self.analysis_results = []
        self.extracted_features = {}

    def load_file_from_selector(self, file_info):
        """从文件选择器加载文件"""
        try:
            file_path = file_info.get('完整路径', '')
            if not file_path or not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
                return

            self.current_file_info = file_info
            self.load_tdms_file(file_path)

            # 更新文件信息显示，包含数据库中的元信息
            file_name = os.path.basename(file_path)
            vehicle = file_info.get('车型', '未知')
            component = file_info.get('部件', '未知')
            sensor_type = file_info.get('传感器类型', '未知')
            sensor_id = file_info.get('传感器编号', '未知')
            test_time = file_info.get('测试时间', '未知')

            # 保存文件信息（不显示在界面上）
            self.current_file_info = {
                'file_name': file_name,
                'vehicle': vehicle,
                'component': component,
                'sensor_type': sensor_type,
                'sensor_id': sensor_id,
                'test_time': test_time,
                'channel_count': len(self.current_channels)
            }

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件失败:\n{str(e)}")

    def set_file_selector_connection(self, file_selector):
        """设置与文件选择器的连接"""
        if hasattr(file_selector, 'file_selected'):
            file_selector.file_selected.connect(self.load_file_from_selector)

    def extract_features_for_diagnosis(self):
        """提取数值特征用于故障判断"""
        if not self.current_file_path or not self.selected_channel:
            print("缺少文件路径或通道信息")
            return

        try:
            # 重新读取TDMS文件获取通道数据（与start_analysis方法保持一致）
            from nptdms import TdmsFile
            with TdmsFile.read(self.current_file_path) as tdms_file:
                # 解析通道名称
                if '/' in self.selected_channel:
                    group_name, channel_name = self.selected_channel.split('/', 1)
                    channel = tdms_file[group_name][channel_name]
                else:
                    # 如果没有组名，尝试在所有组中查找
                    channel = None
                    for group in tdms_file.groups():
                        for ch in group.channels():
                            if ch.name == self.selected_channel:
                                channel = ch
                                break
                        if channel:
                            break

                if channel is None:
                    print(f"找不到通道: {self.selected_channel}")
                    return

                data = channel[:]

            if data is None or len(data) == 0:
                print("通道数据为空")
                return

            # 使用新的算法接口提取特征
            algorithm_interface = get_algorithm_interface(self.sample_rate)
            self.extracted_features = algorithm_interface.extract_numerical_features(data)

            print(f"提取了 {len(self.extracted_features)} 个特征用于故障判断")
            print(f"特征列表: {list(self.extracted_features.keys())}")

        except Exception as e:
            print(f"特征提取失败: {e}")
            import traceback
            traceback.print_exc()
            self.extracted_features = {}

    def open_fault_diagnosis(self):
        """打开故障判断页面"""
        if not self.extracted_features:
            QMessageBox.warning(self, "警告", "没有可用的特征数据，请先进行特征分析")
            return

        try:
            # 寻找主窗口
            main_window = self
            while main_window.parent():
                main_window = main_window.parent()
                if hasattr(main_window, 'navigation'):
                    break

            if hasattr(main_window, 'navigation') and hasattr(main_window, 'fault_diagnosis'):
                # 传递特征数据到故障判断页面
                main_window.fault_diagnosis.set_feature_data(
                    self.extracted_features,
                    self.current_file_info,
                    self.selected_channel
                )
                # 切换到故障判断页面（第4个页面，索引为3）
                main_window.navigation.set_current_page(3)  # 故障判断页面索引
            else:
                QMessageBox.information(self, "提示", "故障判断功能正在开发中...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开故障判断页面失败:\n{str(e)}")

    def open_config_dialog(self):
        """打开特征提取配置对话框"""
        if not self.current_file_path:
            QMessageBox.warning(self, "提示", "请先选择TDMS文件！")
            return

        try:
            config_dialog = FeatureExtractionConfigDialog(self, self.current_file_path)
            if config_dialog.exec_() == QDialog.Accepted:
                config = config_dialog.get_config()

                # 更新主界面的配置信息
                self.selected_channel = config['channel']
                self.sample_rate = config['sample_rate']
                self.selected_methods = config['methods']

                # 更新显示
                self.update_method_display()
                self.update_analyze_button_state()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开配置对话框失败:\n{str(e)}")

    def update_method_display(self):
        """更新方法显示"""
        if self.selected_methods:
            methods_text = f"已配置: {self.selected_channel}\n"
            methods_text += f"采样率: {self.sample_rate}Hz\n"
            methods_text += "特征提取方法:\n" + "\n".join([f"• {method}" for method in self.selected_methods])
        else:
            methods_text = "未选择任何方法"
        self.selected_methods_label.setText(methods_text)




class FeatureExtractionConfigDialog(QDialog):
    """特征提取配置对话框"""

    def __init__(self, parent=None, tdms_file_path=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.tdms_file_path = tdms_file_path
        self.selected_channel = None
        self.sample_rate = 1000
        self.selected_methods = []

        self.init_ui()
        self.load_channels()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("特征提取配置")
        self.setModal(True)
        self.resize(600, 500)

        # 设置窗口样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
                font-family: 'Microsoft YaHei';
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 标题
        title_label = QLabel("特征提取配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: 600;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
                font-family: 'Microsoft YaHei';
            }}
        """)
        layout.addWidget(title_label)

        # 通道选择区域
        channel_group = QGroupBox("🔧 通道选择")
        channel_group.setStyleSheet(self.get_group_box_style())
        channel_layout = QVBoxLayout(channel_group)
        channel_layout.setContentsMargins(15, 20, 15, 15)
        channel_layout.setSpacing(12)

        # 通道选择下拉框
        channel_label = QLabel("选择通道:")
        channel_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 3px;
                font-family: 'Microsoft YaHei';
            }}
        """)
        channel_layout.addWidget(channel_label)

        self.channel_combo = QComboBox()
        self.channel_combo.currentTextChanged.connect(self.on_channel_changed)
        self.channel_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: white;
                border: 1px solid #e0e6ed;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 14px;
                color: {TEXT_PRIMARY};
                min-height: 32px;
                font-family: 'Microsoft YaHei';
            }}
            QComboBox:hover {{
                border-color: {ACCENT_COLOR};
            }}
            QComboBox:focus {{
                border-color: {ACCENT_COLOR};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            QComboBox::down-arrow {{
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI2I2YmVjMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                width: 12px;
                height: 8px;
            }}
        """)
        channel_layout.addWidget(self.channel_combo)

        # 采样率设置
        sample_rate_layout = QHBoxLayout()
        sample_rate_layout.setSpacing(8)

        sample_rate_label = QLabel("采样率(Hz):")
        sample_rate_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
            }}
        """)
        sample_rate_layout.addWidget(sample_rate_label)

        self.sample_rate_input = QLineEdit("1000")
        self.sample_rate_input.setMaximumWidth(120)
        self.sample_rate_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 1px solid #e0e6ed;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: {TEXT_PRIMARY};
                font-family: 'Microsoft YaHei';
            }}
            QLineEdit:hover {{
                border-color: {ACCENT_COLOR};
            }}
            QLineEdit:focus {{
                border-color: {ACCENT_COLOR};
            }}
        """)
        sample_rate_layout.addWidget(self.sample_rate_input)
        sample_rate_layout.addStretch()
        channel_layout.addLayout(sample_rate_layout)

        layout.addWidget(channel_group)

        # 特征提取方法选择区域
        method_group = QGroupBox("⚙️ 特征提取方法")
        method_group.setStyleSheet(self.get_group_box_style())
        method_layout = QVBoxLayout(method_group)
        method_layout.setContentsMargins(15, 20, 15, 15)
        method_layout.setSpacing(12)

        # 选择方法按钮
        self.select_methods_btn = QPushButton("选择特征提取方法")
        self.select_methods_btn.clicked.connect(self.open_method_selector)
        self.select_methods_btn.setStyleSheet(self.get_primary_button_style())
        method_layout.addWidget(self.select_methods_btn)

        # 已选择方法显示区域
        self.selected_methods_label = QLabel("未选择任何方法")
        self.selected_methods_label.setWordWrap(True)
        self.selected_methods_label.setStyleSheet(self.get_info_label_style())
        method_layout.addWidget(self.selected_methods_label)

        layout.addWidget(method_group)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet(self.get_secondary_button_style())
        button_layout.addWidget(cancel_btn)

        # 确定按钮
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setEnabled(False)
        self.ok_btn.setStyleSheet(self.get_primary_button_style())
        button_layout.addWidget(self.ok_btn)

        layout.addLayout(button_layout)

    def get_group_box_style(self):
        """获取统一的分组框样式"""
        return f"""
            QGroupBox {{
                font-size: 15px;
                font-weight: 600;
                color: {TEXT_PRIMARY};
                border: 1px solid #e0e6ed;
                border-radius: 12px;
                margin-top: 8px;
                padding-top: 15px;
                background-color: white;
                font-family: 'Microsoft YaHei';
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {ACCENT_COLOR};
                font-weight: 600;
                background-color: white;
            }}
        """

    def get_primary_button_style(self):
        """获取主要按钮样式"""
        return f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-size: 14px;
                font-weight: 500;
                min-height: 40px;
                padding: 10px 20px;
                border-radius: 10px;
                border: none;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #0052a3;
            }}
            QPushButton:pressed {{
                background-color: #004080;
            }}
            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #999999;
            }}
        """

    def get_secondary_button_style(self):
        """获取次要按钮样式"""
        return f"""
            QPushButton {{
                background-color: #f8f9fa;
                color: {TEXT_PRIMARY};
                font-size: 13px;
                font-weight: 500;
                min-height: 36px;
                padding: 8px 16px;
                border-radius: 8px;
                border: 1px solid #e0e6ed;
                font-family: 'Microsoft YaHei';
            }}
            QPushButton:hover {{
                background-color: #e9ecef;
                border-color: #adb5bd;
            }}
            QPushButton:pressed {{
                background-color: #dee2e6;
                border-color: #6c757d;
            }}
        """

    def get_info_label_style(self):
        """获取信息标签样式"""
        return f"""
            QLabel {{
                color: {TEXT_SECONDARY};
                font-size: 13px;
                padding: 12px 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                min-height: 45px;
                font-family: 'Microsoft YaHei';
                line-height: 1.4;
            }}
        """

    def load_channels(self):
        """加载TDMS文件的通道信息"""
        if not self.tdms_file_path:
            return

        try:
            from nptdms import TdmsFile

            with TdmsFile.read(self.tdms_file_path) as tdms_file:
                channels = []
                for group in tdms_file.groups():
                    for channel in group.channels():
                        channel_name = f"{group.name}/{channel.name}"
                        channels.append(channel_name)

                self.channel_combo.clear()
                self.channel_combo.addItems(channels)

                if channels:
                    self.selected_channel = channels[0]
                    self.check_ready_state()

        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载通道信息失败: {str(e)}")

    def on_channel_changed(self, channel_name):
        """通道选择改变时的处理"""
        self.selected_channel = channel_name
        self.check_ready_state()

    def open_method_selector(self):
        """打开方法选择器"""
        try:
            from ui.feature_method_selector import FeatureMethodSelector

            # 创建方法选择器对话框
            selector = FeatureMethodSelector(self, [])
            selector.methods_selected.connect(self.on_methods_selected)
            selector.exec_()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开方法选择器失败:\n{str(e)}")

    def on_methods_selected(self, method_indices):
        """处理选中的方法"""
        try:


            # 获取方法名称列表
            analysis_methods = [
                {"name": "时域有量纲特征值", "icon": "📊", "short_name": "有量纲\n特征"},
                {"name": "时域无量纲特征值", "icon": "📈", "short_name": "无量纲\n特征"},
                {"name": "自相关函数", "icon": "🔄", "short_name": "自相关\n函数"},
                {"name": "互相关函数", "icon": "↔️", "short_name": "互相关\n函数"},
                {"name": "频谱分析", "icon": "📊", "short_name": "频谱\n(FFT)"},
                {"name": "倒频谱", "icon": "🔄", "short_name": "倒频谱"},
                {"name": "包络谱", "icon": "📦", "short_name": "包络谱"},
                {"name": "阶比谱", "icon": "📏", "short_name": "阶比谱"},
                {"name": "功率谱", "icon": "⚡", "short_name": "功率谱"},
                {"name": "短时傅里叶变换", "icon": "🔍", "short_name": "STFT"},
                {"name": "魏格纳威尔分布", "icon": "🌊", "short_name": "WVD"},
                {"name": "小波变换", "icon": "〰️", "short_name": "小波变换"},
                {"name": "本征模函数", "icon": "🌀", "short_name": "EMD"}
            ]

            self.selected_methods = []
            for idx in method_indices:
                if 0 <= idx < len(analysis_methods):
                    self.selected_methods.append(analysis_methods[idx]["name"])



            self.update_selected_methods_display()
            self.check_ready_state()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理选中方法失败:\n{str(e)}")

    def update_selected_methods_display(self):
        """更新已选择方法的显示"""
        if self.selected_methods:
            methods_text = "已选择方法:\n" + "\n".join([f"• {method}" for method in self.selected_methods])
        else:
            methods_text = "未选择任何方法"
        self.selected_methods_label.setText(methods_text)

    def check_ready_state(self):
        """检查是否可以确定"""
        ready = (self.selected_channel is not None and
                len(self.selected_methods) > 0)
        self.ok_btn.setEnabled(ready)

    def get_config(self):
        """获取配置信息"""
        try:
            sample_rate = int(self.sample_rate_input.text())
        except ValueError:
            sample_rate = 1000

        return {
            'channel': self.selected_channel,
            'sample_rate': sample_rate,
            'methods': self.selected_methods
        }

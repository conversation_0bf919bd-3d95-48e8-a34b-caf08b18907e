# 地面数据分析决策系统

轴承故障诊断专业版 v1.0

## 系统简介

这是一个基于PyQt5开发的现代化轴承故障诊断系统，具有以下特点：

- 🎨 现代化深色主题界面
- 📊 专业的数据可视化
- 🔍 智能故障诊断算法
- 📁 TDMS文件管理
- 📋 自动报告生成
- ⚡ 高性能数据处理

## 功能模块

### 1. 登录系统
- 用户身份验证
- 数据库连接测试
- 记住登录信息

### 2. 仪表盘
- 系统状态监控
- 统计数据展示
- 快速操作入口

### 3. TDMS文件管理
- 文件列表查看
- 多条件搜索筛选
- 文件选择和预览

### 4. 特征提取与分析
- 12种信号特征提取
- 实时可视化展示
- 特征对比分析

### 5. 分类器监测
- 经典机器学习算法（KNN、SVM、随机森林等）
- 深度学习模型
- 训练过程可视化

### 6. 故障报警系统
- 实时状态监控
- 多级报警分类
- 报警历史记录

### 7. 报告生成
- 自动化报告生成
- 多格式导出（PDF、HTML、DOCX）
- 模板化设计

## 安装说明

### 环境要求

- Python 3.7+
- MySQL 5.7+ 或 MariaDB 10.3+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd bearing_fault_diagnose
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置数据库**

创建MySQL数据库并导入表结构：
```bash
mysql -u root -p < database_schema.sql
```

4. **配置系统**

编辑 `config.ini` 文件，设置数据库连接参数：
```ini
[DATABASE]
host = localhost
port = 3306
user = root
password = your_password
database = tdms_db
charset = utf8mb4
```

5. **运行系统**
```bash
python main.py
```

## 使用说明

### 首次登录

1. 启动系统后会显示登录界面
2. 默认用户名：`admin`，密码：`admin`
3. 点击"测试数据库连接"确保数据库连接正常
4. 点击"登录系统"进入主界面

### TDMS文件管理

1. 在主界面点击"文件选择"或使用快速操作
2. 系统会自动从数据库加载TDMS文件列表
3. 可以使用搜索功能按车型、部件、传感器类型等条件筛选
4. 双击文件或点击"选择此文件"确认选择

### 数据库表结构

系统使用以下主要数据表：

- `tdms_files`: TDMS文件信息表
- `users`: 用户管理表
- `diagnosis_results`: 故障诊断结果表
- `feature_extraction`: 特征提取结果表
- `system_config`: 系统配置表
- `operation_logs`: 操作日志表

**重要**: TDMS文件的完整路径由两个字段组成：
- `TDMS文件路径`: 文件存储目录
- `TDMS文件名`: 文件名（不含.tdms扩展名）
- 完整路径 = `TDMS文件路径` + `TDMS文件名` + `.tdms`

## 项目结构

```
bearing_fault_diagnose/
├── main.py                 # 主程序入口
├── config.ini             # 配置文件
├── requirements.txt       # 依赖包列表
├── database_schema.sql    # 数据库表结构
├── 设计.md               # 系统设计文档
├── database/              # 数据库模块
│   ├── __init__.py
│   └── db_manager.py      # 数据库管理器
├── ui/                    # 用户界面模块
│   ├── __init__.py
│   ├── styles.py          # 样式表定义
│   ├── login_window.py    # 登录窗口
│   ├── main_window.py     # 主窗口
│   ├── dashboard.py       # 仪表盘
│   └── file_selector.py   # 文件选择器
├── data/                  # 数据目录
├── temp/                  # 临时文件目录
├── exports/               # 导出文件目录
└── logs/                  # 日志目录
```

## 开发说明

### 技术栈

- **界面框架**: PyQt5
- **数据库**: MySQL/MariaDB + PyMySQL
- **数据处理**: Pandas, NumPy
- **可视化**: Matplotlib, Seaborn
- **机器学习**: Scikit-learn
- **文件处理**: nptdms

### 样式设计

系统采用现代化深色主题，主要配色：
- 主背景色: `#1e1e2e`
- 次级背景: `#252535`
- 强调色: `#6c5ce7`
- 高亮色: `#00cec9`

### 扩展开发

1. **添加新的分析算法**: 在相应模块中实现算法类
2. **自定义界面组件**: 继承PyQt5基础组件
3. **数据库扩展**: 修改`db_manager.py`添加新的数据操作方法

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证config.ini中的数据库配置
   - 确认数据库用户权限

2. **TDMS文件路径错误**
   - 检查数据库中的文件路径是否正确
   - 确认文件实际存在于指定路径

3. **界面显示异常**
   - 检查PyQt5是否正确安装
   - 确认系统支持所需的字体

### 日志查看

系统日志保存在以下位置：
- 应用日志: `system.log`
- 数据库日志: 数据库中的`operation_logs`表

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系开发团队。

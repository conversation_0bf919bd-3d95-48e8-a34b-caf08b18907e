#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的多特征马氏距离分类子界面
只保留特征空间分析表格和马氏距离对比图表
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simplified_mahalanobis_dialog():
    """测试简化后的马氏距离分类子界面"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("简化版马氏距离分类测试")
    main_window.resize(500, 350)
    
    # 设置窗口样式
    main_window.setStyleSheet("""
        QMainWindow {
            background-color: #1e1e2e;
            color: white;
            font-family: 'Microsoft YaHei';
        }
    """)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(15)
    layout.setContentsMargins(25, 25, 25, 25)
    
    # 标题
    title_label = QLabel("简化版马氏距离分类测试")
    title_label.setStyleSheet("""
        QLabel {
            font-size: 16px;
            font-weight: bold;
            color: #6c5ce7;
            margin-bottom: 15px;
        }
    """)
    title_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(title_label)
    
    # 说明文字
    info_label = QLabel("""
    简化内容：
    ✓ 保留特征空间分析表格
    ✓ 保留马氏距离对比图表
    ✗ 删除多特征马氏距离分类分析图
    
    修复内容：
    • 修复了图表显示为空的问题
    • 增加了数据验证和默认值
    • 优化了错误处理机制
    """)
    info_label.setStyleSheet("""
        QLabel {
            font-size: 11px;
            color: #a0a0a0;
            background-color: #2d2d3d;
            padding: 12px;
            border-radius: 6px;
            line-height: 1.4;
        }
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试简化版子界面")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c5ce7;
            color: white;
            font-size: 13px;
            font-weight: 500;
            min-height: 45px;
            padding: 12px 20px;
            border-radius: 8px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
        QPushButton:hover {
            background-color: #5a4fcf;
        }
        QPushButton:pressed {
            background-color: #4834d4;
        }
    """)
    
    # 结果显示区域
    result_label = QLabel("点击按钮开始测试...")
    result_label.setStyleSheet("""
        QLabel {
            font-size: 11px;
            color: #00d4aa;
            background-color: #2d2d3d;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #4d4d6d;
        }
    """)
    result_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(result_label)
    
    def open_dialog():
        """打开马氏距离分类子界面"""
        try:
            result_label.setText("正在打开子界面...")
            app.processEvents()
            
            print("=" * 50)
            print("测试简化版马氏距离分类子界面")
            print("=" * 50)
            
            from ui.fault_diagnosis import MultiFeatureMahalanobisDialog
            
            # 创建测试特征数据
            test_features = {
                '均方根': 1.750195,
                '偏度': -0.043924,
                '峰度': -1.245920,
                '峰值因子': 1.938262,
                '裕度因子': 2.530399,
                '脉冲因子': 2.246699
            }
            
            print(f"测试特征: {list(test_features.keys())}")
            
            result_label.setText("子界面已打开，请查看图表显示...")
            app.processEvents()
            
            # 创建并显示子界面
            dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                result_label.setText("✓ 测试完成 - 子界面正常关闭")
                
                results = dialog.get_results()
                if results:
                    classification = results.get('classification_result', 'unknown')
                    pos_dist = results.get('mahalanobis_distance_positive', 'N/A')
                    neg_dist = results.get('mahalanobis_distance_negative', 'N/A')
                    
                    print("=" * 50)
                    print("测试结果:")
                    print(f"分类结果: {classification}")
                    print(f"到正样本距离: {pos_dist}")
                    print(f"到负样本距离: {neg_dist}")
                    print("图表显示: 应该看到两个图表")
                    print("1. 特征空间分析表格")
                    print("2. 马氏距离对比图表")
                    print("=" * 50)
                    
                    result_text = f"✓ 测试成功\n分类: {classification}\n正样本距离: {pos_dist}\n负样本距离: {neg_dist}"
                    result_label.setText(result_text)
                else:
                    result_label.setText("⚠️ 未获取到分析结果")
                    print("警告: 未获取到分析结果")
            else:
                result_label.setText("❌ 用户取消了操作")
                print("用户取消了操作")
                
        except Exception as e:
            error_msg = f"❌ 测试失败: {str(e)}"
            result_label.setText(error_msg)
            print(error_msg)
            print("详细错误信息:")
            import traceback
            traceback.print_exc()
    
    test_btn.clicked.connect(open_dialog)
    layout.addWidget(test_btn)
    layout.addWidget(result_label)
    
    layout.addStretch()
    
    # 显示主窗口
    main_window.show()
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    print("简化版马氏距离分类子界面测试")
    print("=" * 50)
    print("修改内容:")
    print("1. 删除了多特征马氏距离分类分析图")
    print("2. 保留了特征空间分析表格")
    print("3. 保留了马氏距离对比图表")
    print("4. 修复了图表显示为空的问题")
    print("5. 增加了数据验证和默认值")
    print("6. 调整了窗口大小为 1000x700")
    print("=" * 50)
    
    try:
        exit_code = test_simplified_mahalanobis_dialog()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

# 故障判断功能说明

## 功能概述

故障判断模块是地面数据分析决策系统的核心功能之一，基于已提取的特征进行智能故障诊断。该模块采用马氏距离方法，支持两种分析模式：多特征分类和单特征阈值分析。

## 主要特性

### 1. 特征选择
- **灵活特征选择**: 支持选择1个特征（单特征分析）或3-6个特征（多特征分析）
- **推荐特征**: 系统提供基于经验的推荐特征组合
- **实时验证**: 实时检查特征选择的有效性和适用性

### 2. 多特征马氏距离分类
- **特征空间构建**: 基于3-6个显著特征构建多维特征空间
- **正负样本建模**: 分别计算正样本（正常状态）和负样本（故障状态）的期望值和协方差矩阵
- **马氏距离分类**: 计算当前样本到正负样本中心的马氏距离，进行分类决策
- **特征空间分析**: 详细分析各特征在正负样本中的分布特性

### 3. 单特征马氏距离阈值
- **单特征分析**: 选择1个显著特征进行深入分析
- **标准差计算**: 分别计算正样本和负样本的标准差
- **阈值确定**: 基于马氏距离原理计算最优分类阈值
- **分布可视化**: 直观显示正负样本分布和当前值位置

## 支持的特征类型

### 时域特征
1. **有量纲特征**
   - 均值、均方根、方差、标准差
   - 偏度、峰度、最大值、最小值、峰峰值
   - 脉冲因子、裕度因子、波形因子、峰值因子

2. **无量纲特征**
   - 方差因子

### 频域特征
3. **频谱特征**
   - 主频率、主频率幅值
   - 频谱重心、频谱方差

### 包络特征
4. **包络分析**
   - 包络均值、包络标准差
   - 包络峰值因子

## 使用方法

### 1. 前置条件
- 在"特征提取与分析"页面完成特征分析
- 确保已提取足够的数值特征（建议至少6个）

### 2. 进入故障判断
- 在特征提取页面点击"故障判断"按钮
- 系统自动跳转到故障判断页面并传递特征数据

### 3. 选择特征
- 从特征列表中选择3-6个代表性特征
- 可使用"选择推荐特征"快速选择经验推荐的特征组合
- 系统实时显示选择状态和有效性

### 4. 执行诊断
- **多特征马氏距离分类**: 选择3-6个特征后，点击"多特征马氏距离分类"按钮
- **单特征马氏距离阈值**: 选择1个特征后，点击"单特征马氏距离阈值"按钮

### 5. 查看结果
- 查看诊断状态（正常/警告/故障）
- 分析异常特征详情
- 查看可视化图表和统计信息

## 诊断算法详解

### 多特征马氏距离分类
```
1. 生成正样本数据集 X_pos (50个样本，正常状态)
2. 生成负样本数据集 X_neg (50个样本，故障状态)
3. 计算正样本统计参数：
   - 期望值向量 μ_pos = mean(X_pos)
   - 协方差矩阵 Σ_pos = cov(X_pos)
4. 计算负样本统计参数：
   - 期望值向量 μ_neg = mean(X_neg)
   - 协方差矩阵 Σ_neg = cov(X_neg)
5. 计算当前样本到正样本的马氏距离：
   d_pos = √[(x-μ_pos)ᵀ Σ_pos⁻¹ (x-μ_pos)]
6. 计算当前样本到负样本的马氏距离：
   d_neg = √[(x-μ_neg)ᵀ Σ_neg⁻¹ (x-μ_neg)]
7. 分类决策：if d_pos < d_neg then 正常 else 故障
```

### 单特征马氏距离阈值
```
1. 生成正样本数据 x_pos (100个样本)
2. 生成负样本数据 x_neg (100个样本)
3. 计算正样本统计参数：
   - 均值 μ_pos = mean(x_pos)
   - 标准差 σ_pos = std(x_pos)
4. 计算负样本统计参数：
   - 均值 μ_neg = mean(x_neg)
   - 标准差 σ_neg = std(x_neg)
5. 计算分类阈值：threshold = (μ_pos + μ_neg) / 2
6. 计算马氏距离：d = |x - μ_pos| / σ_pos
7. 分类决策：if x ≤ threshold then 正常 else 故障
```

## 推荐特征组合

基于轴承故障诊断经验，推荐以下特征组合：

### 标准组合
- 均方根 (RMS)
- 峰值因子 (Crest Factor)
- 脉冲因子 (Impulse Factor)
- 裕度因子 (Clearance Factor)
- 峰度 (Kurtosis)
- 偏度 (Skewness)

### 简化组合（3-4个特征）
- 均方根
- 峰值因子
- 峰度
- 脉冲因子

## 结果解读

### 多特征分类结果
- **分类结果**: 基于马氏距离比较的分类决策（正常/故障）
- **到正样本距离**: 当前样本到正常状态中心的马氏距离
- **到负样本距离**: 当前样本到故障状态中心的马氏距离
- **决策依据**: 距离哪个样本中心更近
- **特征空间分析**: 各特征在正负样本中的分布对比

### 单特征阈值结果
- **分类阈值**: 基于正负样本均值计算的最优分类边界
- **马氏距离**: 当前值到正样本分布中心的标准化距离
- **分布可视化**: 正负样本的概率分布曲线和当前值位置
- **统计参数**: 正负样本的均值和标准差

## 注意事项

1. **特征选择**: 建议选择物理意义明确、对故障敏感的特征
2. **数据质量**: 确保输入数据的质量和完整性
3. **阈值调整**: 可根据具体设备和工况调整阈值参数
4. **结合分析**: 建议结合时频域分析结果进行综合判断
5. **趋势监测**: 单次诊断结果需结合历史趋势分析

## 技术特点

### 界面设计
- 现代化的PyQt5界面
- 直观的特征选择和结果显示
- 丰富的可视化图表

### 算法优势
- 多种诊断方法互补
- 统计学理论支撑
- 工程经验结合

### 扩展性
- 支持自定义阈值
- 可添加新的诊断算法
- 支持批量诊断

## 测试方法

### 使用测试脚本
```bash
python test_fault_diagnosis.py
```

### 完整系统测试
```bash
python main.py
```
然后按以下步骤操作：
1. 登录系统
2. 选择TDMS文件
3. 进行特征提取
4. 点击"故障判断"按钮
5. 选择特征并执行诊断

## 依赖要求

- PyQt5 >= 5.15.0
- numpy >= 1.20.0
- matplotlib >= 3.5.0
- scipy >= 1.7.0
- scikit-learn >= 1.0.0

故障判断功能现已完成，为轴承故障诊断提供了专业、可靠的智能分析工具！

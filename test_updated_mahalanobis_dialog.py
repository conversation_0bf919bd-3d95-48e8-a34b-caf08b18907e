#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的多特征马氏距离分类子界面
验证子界面只显示两个图表，主界面显示简化结果
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_updated_mahalanobis_dialog():
    """测试更新后的马氏距离分类子界面"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("更新后的马氏距离分类测试")
    main_window.resize(500, 400)
    
    # 设置窗口样式
    main_window.setStyleSheet("""
        QMainWindow {
            background-color: #1e1e2e;
            color: white;
            font-family: 'Microsoft YaHei';
        }
    """)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(20)
    layout.setContentsMargins(30, 30, 30, 30)
    
    # 标题
    title_label = QLabel("多特征马氏距离分类测试")
    title_label.setStyleSheet("""
        QLabel {
            font-size: 18px;
            font-weight: bold;
            color: #6c5ce7;
            margin-bottom: 20px;
        }
    """)
    title_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(title_label)
    
    # 说明文字
    info_label = QLabel("""
    测试说明：
    1. 点击下方按钮将打开马氏距离分类子界面
    2. 子界面将显示三个图表：
       - 特征空间分析表格
       - 马氏距离对比图表
       - 多特征马氏距离分类分析图
    3. 关闭子界面后，主界面将显示简化的分类结果
    4. 原来的详细图表已移至子界面中
    """)
    info_label.setStyleSheet("""
        QLabel {
            font-size: 12px;
            color: #a0a0a0;
            background-color: #2d2d3d;
            padding: 15px;
            border-radius: 8px;
            line-height: 1.5;
        }
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("打开马氏距离分类子界面")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c5ce7;
            color: white;
            font-size: 14px;
            font-weight: 500;
            min-height: 50px;
            padding: 15px 25px;
            border-radius: 10px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
        QPushButton:hover {
            background-color: #5a4fcf;
        }
        QPushButton:pressed {
            background-color: #4834d4;
        }
    """)
    
    # 结果显示区域
    result_label = QLabel("点击按钮开始测试...")
    result_label.setStyleSheet("""
        QLabel {
            font-size: 12px;
            color: #00d4aa;
            background-color: #2d2d3d;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #4d4d6d;
        }
    """)
    result_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(result_label)
    
    def open_dialog():
        """打开马氏距离分类子界面"""
        try:
            result_label.setText("正在打开子界面...")
            app.processEvents()
            
            from ui.fault_diagnosis import MultiFeatureMahalanobisDialog
            
            # 创建测试特征数据
            test_features = {
                '均值': 0.05,
                '标准差': 0.8,
                '方差': 0.64,
                '峰值因子': 1.5,
                '偏度': 0.2,
                '峰度': 3.2
            }
            
            result_label.setText(f"测试特征: {', '.join(test_features.keys())}")
            app.processEvents()
            
            # 创建并显示子界面
            dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                result_label.setText("✓ 子界面已关闭，分析结果已获取")
                results = dialog.get_results()
                if results:
                    classification = results.get('classification_result', 'unknown')
                    pos_dist = results.get('mahalanobis_distance_positive', 'N/A')
                    neg_dist = results.get('mahalanobis_distance_negative', 'N/A')
                    
                    result_text = f"""
✓ 子界面测试完成
分类结果: {classification}
到正样本距离: {pos_dist}
到负样本距离: {neg_dist}

说明: 详细的三个图表已在子界面中显示
- 特征空间分析表格
- 马氏距离对比图表
- 多特征马氏距离分类分析图
主界面现在只显示简化的分类结果
                    """
                    result_label.setText(result_text.strip())
                else:
                    result_label.setText("⚠️ 未获取到分析结果")
            else:
                result_label.setText("❌ 用户取消了操作")
                
        except Exception as e:
            result_label.setText(f"❌ 测试失败: {str(e)}")
            print(f"详细错误信息: {e}")
            import traceback
            traceback.print_exc()
    
    test_btn.clicked.connect(open_dialog)
    layout.addWidget(test_btn)
    layout.addWidget(result_label)
    
    layout.addStretch()
    
    # 显示主窗口
    main_window.show()
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    print("更新后的马氏距离分类子界面测试")
    print("=" * 60)
    print("测试内容:")
    print("1. 子界面显示三个图表：")
    print("   - 特征空间分析表格")
    print("   - 马氏距离对比图表")
    print("   - 多特征马氏距离分类分析图")
    print("2. 主界面显示简化的分类结果")
    print("3. 验证图表样式是否符合要求")
    print("=" * 60)
    
    try:
        exit_code = test_updated_mahalanobis_dialog()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

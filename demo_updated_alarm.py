#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
展示更新后的故障异常报警系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import PRIMARY_BG, ACCENT_COLOR, TEXT_PRIMARY

class UpdatedDemoWindow(QMainWindow):
    """更新后的演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.show_alarm_system()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("更新后的故障异常报警系统")
        self.setGeometry(50, 50, 1400, 900)
        self.setStyleSheet(f"background-color: {PRIMARY_BG};")
    
    def show_alarm_system(self):
        """直接显示故障异常报警系统"""
        self.alarm_system = FaultAlarmSystem()
        self.setCentralWidget(self.alarm_system)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("更新后的故障异常报警系统")
    app.setApplicationVersion("1.1")
    
    # 创建演示窗口
    demo = UpdatedDemoWindow()
    demo.show()
    
    print("🎉 更新后的故障异常报警系统已启动")
    print("\n📋 更新内容：")
    print("✅ 按钮位置下移，增加了间距")
    print("✅ 卡片尺寸拉长：350x280 → 420x320")
    print("✅ 采用经典分类器页面的配色方案")
    print("✅ 字体大小放大：")
    print("   - 卡片标题：18px → 22px")
    print("   - 状态标签：12px → 16px")
    print("   - 数据项：14px → 18px")
    print("   - 进度条标签：14px → 18px")
    print("✅ 卡片背景色：白色 → 浅蓝色")
    print("✅ 边框颜色：灰色 → 蓝色")
    print("✅ 分隔线颜色：灰色 → 蓝色")
    print("✅ 进度条高度：8px → 12px")
    print("\n🎨 配色方案参考经典分类器页面：")
    print("• 卡片背景：SECONDARY_BG (浅蓝色)")
    print("• 边框颜色：ACCENT_COLOR (蓝色)")
    print("• 标题颜色：ACCENT_COLOR (蓝色)")
    print("• 数据标签：ACCENT_COLOR (蓝色)")
    print("\n💡 设计改进：")
    print("• 更大的卡片尺寸提供更好的可读性")
    print("• 更大的字体确保内容清晰可见")
    print("• 统一的配色方案保持系统一致性")
    print("• 合理的按钮间距改善布局")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)

if __name__ == "__main__":
    main()

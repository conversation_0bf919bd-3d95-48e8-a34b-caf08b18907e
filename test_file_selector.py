#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文件选择器的修改效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt
from database.db_manager import DatabaseManager
from ui.file_selector import FileSelector

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件选择器测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b3d;
                color: #ffffff;
            }
        """)
        
        # 创建数据库管理器
        self.db_manager = DatabaseManager()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建文件选择器
        self.file_selector = FileSelector(self.db_manager)
        layout.addWidget(self.file_selector)
        
        # 连接信号
        self.file_selector.file_selected.connect(self.on_file_selected)
    
    def on_file_selected(self, file_info):
        """文件选择回调"""
        print(f"选择的文件: {file_info}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()

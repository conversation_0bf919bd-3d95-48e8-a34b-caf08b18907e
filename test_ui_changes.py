#!/usr/bin/env python3
"""
测试界面修改的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from database.db_manager import DatabaseManager
from ui.main_window import MainWindow

def test_main_window():
    """测试主窗口"""
    app = QApplication(sys.argv)
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    
    # 创建主窗口
    window = MainWindow(db_manager)
    window.show()
    
    print("主窗口已启动，窗口大小固定为1280*900")
    print("请测试特征分析页面的新功能：")
    print("1. 点击导航栏中的'特征提取与分析'")
    print("2. 点击'选择特征提取方法'按钮")
    print("3. 在弹出的子页面中选择方法")
    print("4. 确认选择后查看主界面的显示")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_main_window()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Qt原生马氏距离对比组件
不依赖matplotlib，使用Qt原生组件显示
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qt_native_chart():
    """测试Qt原生马氏距离对比组件"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("Qt原生马氏距离对比测试")
    main_window.resize(450, 300)
    
    # 设置窗口样式
    main_window.setStyleSheet("""
        QMainWindow {
            background-color: #1e1e2e;
            color: white;
            font-family: 'Microsoft YaHei';
        }
    """)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(15)
    layout.setContentsMargins(20, 20, 20, 20)
    
    # 标题
    title_label = QLabel("Qt原生马氏距离对比测试")
    title_label.setStyleSheet("""
        QLabel {
            font-size: 16px;
            font-weight: bold;
            color: #6c5ce7;
            margin-bottom: 10px;
        }
    """)
    title_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(title_label)
    
    # 说明文字
    info_label = QLabel("""
    新的实现方式：
    ✓ 不依赖matplotlib
    ✓ 使用Qt原生组件
    ✓ 绿色框显示正样本距离
    ✓ 红色框显示负样本距离
    ✓ 显示分类结果说明
    
    预期效果：
    • 两个彩色的距离显示框
    • 清晰的数值显示
    • 分类结果说明
    """)
    info_label.setStyleSheet("""
        QLabel {
            font-size: 10px;
            color: #a0a0a0;
            background-color: #2d2d3d;
            padding: 10px;
            border-radius: 5px;
            line-height: 1.3;
        }
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试Qt原生组件")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c5ce7;
            color: white;
            font-size: 12px;
            font-weight: 500;
            min-height: 40px;
            padding: 10px 15px;
            border-radius: 6px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
        QPushButton:hover {
            background-color: #5a4fcf;
        }
        QPushButton:pressed {
            background-color: #4834d4;
        }
    """)
    
    # 结果显示区域
    result_label = QLabel("点击按钮测试Qt原生组件...")
    result_label.setStyleSheet("""
        QLabel {
            font-size: 10px;
            color: #00d4aa;
            background-color: #2d2d3d;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #4d4d6d;
        }
    """)
    result_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(result_label)
    
    def open_dialog():
        """打开马氏距离分类子界面"""
        try:
            result_label.setText("正在测试Qt原生组件...")
            app.processEvents()
            
            print("=" * 60)
            print("测试Qt原生马氏距离对比组件")
            print("=" * 60)
            
            from ui.fault_diagnosis import MultiFeatureMahalanobisDialog
            
            # 创建测试特征数据
            test_features = {
                '均方根': 1.750195,
                '偏度': -0.043924,
                '峰度': -1.245920,
                '峰值因子': 1.938262,
                '裕度因子': 2.530399,
                '脉冲因子': 2.246699
            }
            
            print("测试数据:")
            for name, value in test_features.items():
                print(f"  {name}: {value}")
            
            result_label.setText("子界面已打开，请检查Qt原生组件...")
            app.processEvents()
            
            # 创建并显示子界面
            dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                result_label.setText("✓ 测试完成")
                
                print("=" * 60)
                print("测试结果:")
                print("请检查子界面中的马氏距离对比组件是否显示：")
                print("1. 是否有绿色的正样本距离框？")
                print("2. 是否有红色的负样本距离框？")
                print("3. 是否显示了具体的数值？")
                print("4. 是否有分类结果说明？")
                print("5. 组件是否不再是空白？")
                print("=" * 60)
                
                results = dialog.get_results()
                if results:
                    pos_dist = results.get('mahalanobis_distance_positive', 'N/A')
                    neg_dist = results.get('mahalanobis_distance_negative', 'N/A')
                    classification = results.get('classification_result', 'unknown')
                    
                    print(f"正样本距离: {pos_dist}")
                    print(f"负样本距离: {neg_dist}")
                    print(f"分类结果: {classification}")
                    
                    result_text = f"✓ Qt原生组件测试完成\n正样本: {pos_dist}\n负样本: {neg_dist}\n分类: {classification}"
                    result_label.setText(result_text)
                else:
                    result_label.setText("⚠️ 未获取到距离数据")
            else:
                result_label.setText("❌ 测试被取消")
                print("测试被用户取消")
                
        except Exception as e:
            error_msg = f"❌ 测试失败: {str(e)}"
            result_label.setText(error_msg)
            print(error_msg)
            print("详细错误信息:")
            import traceback
            traceback.print_exc()
    
    test_btn.clicked.connect(open_dialog)
    layout.addWidget(test_btn)
    layout.addWidget(result_label)
    
    layout.addStretch()
    
    # 显示主窗口
    main_window.show()
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    print("Qt原生马氏距离对比组件测试")
    print("=" * 60)
    print("新的实现方式:")
    print("1. 完全不依赖matplotlib")
    print("2. 使用Qt原生的QFrame和QLabel组件")
    print("3. 绿色框显示正样本距离")
    print("4. 红色框显示负样本距离")
    print("5. 大字体显示具体数值")
    print("6. 显示分类结果说明")
    print("=" * 60)
    print("预期效果:")
    print("- 两个明显的彩色框（绿色和红色）")
    print("- 每个框内显示距离标题和数值")
    print("- 底部显示分类结果说明")
    print("- 完全不依赖图表库，确保显示")
    print("=" * 60)
    
    try:
        exit_code = test_qt_native_chart()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试故障异常报警系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.fault_alarm_system import FaultAlarmSystem

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建故障异常报警系统窗口
    window = FaultAlarmSystem()
    window.show()
    
    # 设置一个定时器来保持窗口显示
    timer = QTimer()
    timer.timeout.connect(lambda: None)
    timer.start(1000)
    
    print("故障异常报警系统已显示，按 Ctrl+C 退出")
    print("功能说明：")
    print("1. 状态栏显示当前系统状态（正常/警告/报警）")
    print("2. 三个监测卡片显示不同算法的检测结果")
    print("3. 点击'历史故障记录'按钮可查看历史记录")
    print("4. 系统会每5秒自动更新状态模拟实时监测")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)

"""
算法模块 - 轴承故障诊断系统
提供特征提取、故障诊断等算法的统一接口
"""

from .feature_extraction import FeatureExtractor
from .fault_diagnosis import FaultDiagnosisAlgorithm
from .signal_processing import SignalProcessor
from .classical_ml import ClassicalMLClassifier
from .deep_learning import DeepLearningClassifier

__version__ = "1.0.0"

__all__ = [
    'FeatureExtractor',
    'FaultDiagnosisAlgorithm',
    'SignalProcessor',
    'ClassicalMLClassifier',
    'DeepLearningClassifier',
    'AlgorithmInterface',
    'create_feature_extractor',
    'create_fault_diagnoser',
    'create_signal_processor',
    'create_classical_classifier',
    'create_deep_learning_classifier',
    'get_algorithm_interface',
    'analyze_signal_data',
    'diagnose_from_data',
    'complete_analysis_pipeline'
]


def create_feature_extractor(sample_rate=1000):
    """
    创建特征提取器实例
    
    Args:
        sample_rate (float): 采样率，默认1000Hz
        
    Returns:
        FeatureExtractor: 特征提取器实例
    """
    return FeatureExtractor(sample_rate=sample_rate)


def create_fault_diagnoser():
    """
    创建故障诊断器实例
    
    Returns:
        FaultDiagnosisAlgorithm: 故障诊断器实例
    """
    return FaultDiagnosisAlgorithm()


def create_signal_processor(sample_rate=1000):
    """
    创建信号处理器实例

    Args:
        sample_rate (float): 采样率，默认1000Hz

    Returns:
        SignalProcessor: 信号处理器实例
    """
    return SignalProcessor(sample_rate=sample_rate)


def create_classical_classifier():
    """
    创建传统机器学习分类器实例

    Returns:
        ClassicalMLClassifier: 传统机器学习分类器实例
    """
    return ClassicalMLClassifier()


def create_deep_learning_classifier():
    """
    创建深度学习分类器实例

    Returns:
        DeepLearningClassifier: 深度学习分类器实例
    """
    return DeepLearningClassifier()


# 算法接口类
class AlgorithmInterface:
    """算法接口基类"""
    
    def __init__(self, sample_rate=1000):
        self.sample_rate = sample_rate
        self.feature_extractor = create_feature_extractor(sample_rate)
        self.fault_diagnoser = create_fault_diagnoser()
        self.signal_processor = create_signal_processor(sample_rate)
    
    def extract_features(self, data, methods=None):
        """
        提取特征
        
        Args:
            data (array): 信号数据
            methods (list): 特征提取方法列表，None表示提取所有特征
            
        Returns:
            dict: 提取的特征结果
        """
        return self.feature_extractor.extract_features(data, methods)
    
    def analyze_signal(self, data, method_index, ax=None):
        """
        分析信号
        
        Args:
            data (array): 信号数据
            method_index (int): 分析方法索引
            ax (matplotlib.axes): 绘图轴，可选
            
        Returns:
            dict: 分析结果
        """
        return self.feature_extractor.analyze_signal(data, method_index, ax)
    
    def diagnose_fault(self, features, method='threshold', **kwargs):
        """
        故障诊断

        Args:
            features (dict): 特征数据
            method (str): 诊断方法，'threshold', 'mahalanobis', 'multi_mahalanobis', 'single_mahalanobis', 'advanced_mahalanobis'
            **kwargs: 其他参数

        Returns:
            dict: 诊断结果
        """
        if method == 'threshold':
            return self.fault_diagnoser.diagnose(features, 'threshold')
        elif method == 'mahalanobis':
            return self.fault_diagnoser.diagnose(features, 'mahalanobis')
        elif method == 'multi_mahalanobis':
            return self.fault_diagnoser.multi_feature_mahalanobis_classification(
                features,
                kwargs.get('positive_samples'),
                kwargs.get('negative_samples')
            )
        elif method == 'single_mahalanobis':
            return self.fault_diagnoser.single_feature_mahalanobis_threshold(
                features,
                kwargs.get('positive_samples'),
                kwargs.get('negative_samples')
            )
        elif method == 'advanced_mahalanobis':
            return self.fault_diagnoser.advanced_mahalanobis_diagnosis(features)
        else:
            raise ValueError(f"不支持的诊断方法: {method}")

    def set_normal_baseline(self, normal_features):
        """
        设置正常状态基线

        Args:
            normal_features (dict or list): 正常状态的特征数据
        """
        self.fault_diagnoser.set_normal_baseline(normal_features)

    def set_custom_thresholds(self, feature_thresholds):
        """
        设置自定义阈值

        Args:
            feature_thresholds (dict): 特征阈值字典
        """
        self.fault_diagnoser.set_custom_thresholds(feature_thresholds)

    def get_diagnosis_summary(self, diagnosis_results):
        """
        获取诊断结果摘要

        Args:
            diagnosis_results (dict): 诊断结果

        Returns:
            str: 诊断摘要文本
        """
        return self.fault_diagnoser.get_diagnosis_summary(diagnosis_results)
    
    def process_signal(self, data, operation):
        """
        信号处理
        
        Args:
            data (array): 信号数据
            operation (str): 处理操作
            
        Returns:
            array: 处理后的信号
        """
        return self.signal_processor.process(data, operation)

    def get_method_names(self):
        """
        获取所有可用的分析方法名称

        Returns:
            list: 方法名称列表
        """
        return self.feature_extractor.method_names

    def extract_numerical_features(self, data):
        """
        提取数值特征用于故障判断

        Args:
            data (array): 信号数据

        Returns:
            dict: 提取的数值特征
        """
        return self.feature_extractor.extract_numerical_features(data)

    def get_extracted_features(self):
        """
        获取已提取的特征

        Returns:
            dict: 已提取的特征
        """
        return self.feature_extractor.extracted_features

    def analyze_with_emd_hilbert(self, data):
        """
        使用EMD-希尔伯特变换分析

        Args:
            data (array): 信号数据

        Returns:
            dict: 分析结果
        """
        return self.feature_extractor.emd_processor.analyze_emd_hilbert(data)


# 全局算法接口实例
_algorithm_interface = None


def get_algorithm_interface(sample_rate=1000):
    """
    获取全局算法接口实例

    Args:
        sample_rate (float): 采样率

    Returns:
        AlgorithmInterface: 算法接口实例
    """
    global _algorithm_interface
    if _algorithm_interface is None or _algorithm_interface.sample_rate != sample_rate:
        _algorithm_interface = AlgorithmInterface(sample_rate)
    return _algorithm_interface


# 便捷函数
def analyze_signal_data(data, method_indices=None, sample_rate=1000):
    """
    分析信号数据的便捷函数

    Args:
        data (array): 信号数据
        method_indices (list): 分析方法索引列表，None表示使用所有方法
        sample_rate (float): 采样率

    Returns:
        dict: 分析结果
    """
    interface = get_algorithm_interface(sample_rate)

    if method_indices is None:
        method_indices = list(range(len(interface.get_method_names())))

    results = {}
    for method_idx in method_indices:
        method_name = interface.get_method_names()[method_idx]
        try:
            result = interface.analyze_signal(data, method_idx)
            results[method_name] = result
        except Exception as e:
            results[method_name] = {'error': str(e)}

    return results


def diagnose_from_data(data, method='threshold', sample_rate=1000, **kwargs):
    """
    从信号数据直接进行故障诊断的便捷函数

    Args:
        data (array): 信号数据
        method (str): 诊断方法
        sample_rate (float): 采样率
        **kwargs: 其他参数

    Returns:
        dict: 诊断结果
    """
    interface = get_algorithm_interface(sample_rate)

    # 首先提取特征
    features = interface.extract_numerical_features(data)

    # 然后进行诊断
    return interface.diagnose_fault(features, method, **kwargs)


def complete_analysis_pipeline(data, sample_rate=1000, diagnosis_method='threshold'):
    """
    完整的分析流水线

    Args:
        data (array): 信号数据
        sample_rate (float): 采样率
        diagnosis_method (str): 诊断方法

    Returns:
        dict: 包含特征提取和故障诊断结果的完整分析结果
    """
    interface = get_algorithm_interface(sample_rate)

    # 特征提取
    features = interface.extract_numerical_features(data)

    # 故障诊断
    diagnosis = interface.diagnose_fault(features, diagnosis_method)

    # 获取诊断摘要
    summary = interface.get_diagnosis_summary(diagnosis)

    return {
        'features': features,
        'diagnosis': diagnosis,
        'summary': summary,
        'sample_rate': sample_rate,
        'method': diagnosis_method
    }

# 深度学习监测页面改动说明

## 改动概述

根据用户需求，对深度学习监测页面进行了以下重要改动：

### 1. 训练参数和网络架构配置子页面化

**改动内容：**
- 将原本在主页面的网络架构选择和训练参数配置移至独立的子页面
- 创建了 `TrainingConfigDialog` 类，提供专门的配置界面
- 主页面添加"配置训练参数"按钮，点击打开配置对话框

**新增文件：**
- `ui/training_config_dialog.py` - 训练配置对话框

**配置对话框功能：**
- **网络架构标签页：** 预定义架构选择（简单MLP、深层MLP、宽层MLP）和自定义架构配置
- **训练参数标签页：** 训练轮数、批次大小、学习率等基本参数和高级参数配置
- **数据处理标签页：** 数据预处理选项（标准化、随机打乱等）
- **实时预览：** 显示当前网络架构配置

### 2. 智能模型保存功能

**改动内容：**
- 模型自动保存到 `model/deeplearning_model/` 文件夹
- 根据训练参数自动生成文件名，格式为：`{架构}_{轮数}ep_{批次大小}bs_{学习率}lr_{dropout率}dr_{时间戳}.pth`
- 例如：`简单MLP_100ep_32bs_0.001lr_0.3dr_20231201_143022.pth`

**优势：**
- 文件名包含完整的训练参数信息，便于识别和管理
- 时间戳确保文件名唯一性
- 自动创建保存目录

### 3. 模型测试子页面

**改动内容：**
- 将"加载模型"按钮改为"测试模型"按钮
- 创建了 `ModelTestDialog` 类，提供专门的模型测试界面
- 点击"测试模型"按钮打开测试对话框

**新增文件：**
- `ui/model_test_dialog.py` - 模型测试对话框

**测试对话框功能：**
- **模型加载标签页：** 
  - 浏览和加载模型文件
  - 显示详细的模型信息（架构、参数、训练历史等）
- **数据输入标签页：**
  - 支持CSV文件输入
  - 支持手动输入特征值
  - 实时预测功能
- **测试结果标签页：**
  - 显示预测结果表格
  - 提供统计信息（类别分布、置信度统计等）

## 主要改进

### 用户体验改进
1. **界面简洁化：** 主页面不再显示复杂的配置选项，界面更加清爽
2. **专业化配置：** 配置对话框提供更专业和详细的参数设置选项
3. **智能化管理：** 模型文件自动命名，便于管理和识别
4. **独立测试环境：** 模型测试功能独立，不影响训练流程

### 功能增强
1. **配置预览：** 实时显示网络架构配置
2. **参数验证：** 配置对话框包含参数验证和错误提示
3. **模型信息展示：** 加载模型时显示完整的模型信息
4. **多种输入方式：** 支持文件和手动输入两种测试数据输入方式
5. **详细统计：** 提供预测结果的详细统计分析

## 文件结构

```
ui/
├── deep_learning_classifier.py    # 主页面（已修改）
├── training_config_dialog.py      # 训练配置对话框（新增）
└── model_test_dialog.py          # 模型测试对话框（新增）

model/
└── deeplearning_model/            # 深度学习模型保存目录（新增）
```

## 使用流程

### 训练模型
1. 在深度学习监测页面加载特征数据
2. 点击"配置训练参数"按钮
3. 在配置对话框中设置网络架构和训练参数
4. 确认配置后返回主页面
5. 点击"开始训练"进行模型训练
6. 训练完成后点击"保存模型"，模型将自动保存到指定目录

### 测试模型
1. 点击"测试模型"按钮打开测试对话框
2. 在"模型加载"标签页选择并加载模型文件
3. 在"数据输入"标签页选择测试数据或手动输入特征值
4. 点击"开始预测"进行模型测试
5. 在"测试结果"标签页查看预测结果和统计信息

## 技术实现

### 配置管理
- 使用字典存储当前训练配置
- 配置对话框返回完整的配置字典
- 主页面实时显示当前配置状态

### 模型保存
- 使用datetime生成时间戳
- 自动创建保存目录
- 文件名包含所有关键参数信息

### 异步处理
- 模型测试使用独立线程，避免界面冻结
- 提供进度反馈和错误处理

## 兼容性

- 保持与现有代码的兼容性
- 不影响其他模块的功能
- 可以与现有的特征提取和数据管理模块无缝集成

## 后续扩展

该设计为后续功能扩展预留了空间：
- 可以轻松添加新的网络架构类型
- 可以扩展更多的训练参数选项
- 可以增加模型比较和评估功能
- 可以添加模型导出和部署功能

#!/usr/bin/env python3
"""
测试故障异常报警页面界面改进
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from ui.fault_alarm_system import FaultAlarmSystem

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("故障异常报警系统 - 界面测试")
        self.setGeometry(100, 100, 1280, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        layout.addWidget(self.fault_alarm)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建并显示测试窗口
    window = TestWindow()
    window.show()
    
    print("故障异常报警页面界面测试启动")
    print("改进内容:")
    print("1. 卡片背景色改为柔和的渐变色，避免纯白色")
    print("2. 增加卡片尺寸和内边距，避免信息重叠")
    print("3. 优化标签和数值的对齐方式")
    print("4. 缩短卡片标题，确保文字完全显示")
    print("5. 统一数据项和进度条的样式")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

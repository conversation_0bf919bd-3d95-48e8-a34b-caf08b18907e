#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
故障异常报警系统演示程序
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import PRIMARY_BG, ACCENT_COLOR, TEXT_PRIMARY

class DemoWindow(QMainWindow):
    """演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("故障异常报警系统演示")
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet(f"background-color: {PRIMARY_BG};")
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("🚀 故障异常报警系统演示")
        title.setStyleSheet(f"""
            font-size: 32px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            text-align: center;
            margin: 20px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明文字
        description = QLabel("""
        本演示展示了基于多算法融合的轴承故障异常报警系统。
        
        🎨 设计特点：
        • 参考现代化HTML界面设计，采用卡片式布局
        • 保持与原系统一致的浅蓝色配色方案
        • 响应式设计，支持悬浮效果和动画
        
        📊 功能特色：
        • 实时状态监控：显示系统当前状态（正常/警告/报警）
        • 多算法监测：故障判断、传统分类器、深度学习三种算法
        • 历史记录：独立子页面显示历史故障记录
        • 控制功能：数据刷新、报警消音、报告生成
        
        ⚡ 技术实现：
        • 基于PyQt5框架开发
        • 模块化设计，易于扩展
        • 实时数据更新和状态模拟
        • 完整的信号槽机制
        """)
        description.setStyleSheet(f"""
            font-size: 16px;
            color: {TEXT_PRIMARY};
            line-height: 1.6;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            border: 2px solid #e0e0e0;
        """)
        description.setWordWrap(True)
        layout.addWidget(description)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignCenter)
        
        start_btn = QPushButton("🚀 启动故障异常报警系统")
        start_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 25px;
                padding: 15px 30px;
                font-weight: bold;
                font-size: 18px;
                min-width: 300px;
            }}
            QPushButton:hover {{
                background-color: #0080ff;
            }}
        """)
        start_btn.clicked.connect(self.start_alarm_system)
        button_layout.addWidget(start_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        # 底部信息
        footer = QLabel("轴承故障诊断系统 v1.0 | 故障异常报警模块演示")
        footer.setStyleSheet(f"""
            font-size: 14px;
            color: {TEXT_PRIMARY};
            text-align: center;
            margin: 10px;
        """)
        footer.setAlignment(Qt.AlignCenter)
        layout.addWidget(footer)
    
    def start_alarm_system(self):
        """启动故障异常报警系统"""
        self.alarm_system = FaultAlarmSystem()
        self.alarm_system.show()
        
        # 隐藏演示窗口
        self.hide()
        
        # 当报警系统关闭时显示演示窗口
        self.alarm_system.destroyed.connect(self.show)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("故障异常报警系统演示")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("轴承故障诊断系统")
    
    # 创建演示窗口
    demo = DemoWindow()
    demo.show()
    
    print("🎉 故障异常报警系统演示已启动")
    print("\n📋 演示说明：")
    print("1. 点击'启动故障异常报警系统'按钮开始演示")
    print("2. 观察实时状态更新和卡片显示")
    print("3. 点击'历史故障记录'查看历史数据")
    print("4. 测试各种控制功能")
    print("\n💡 设计亮点：")
    print("• 现代化界面设计，参考HTML模板排版")
    print("• 保持原系统配色方案的一致性")
    print("• 历史记录独立子页面设计")
    print("• 实时数据更新和状态模拟")
    print("• 响应式卡片布局")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)

if __name__ == "__main__":
    main()

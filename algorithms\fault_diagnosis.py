"""
故障诊断算法模块
包含阈值法和马氏距离方法的故障诊断算法
"""

import numpy as np
from scipy.stats import chi2
from scipy.spatial.distance import mahalanobis
from scipy.linalg import inv


class FaultDiagnosisAlgorithm:
    """故障诊断算法类"""
    
    def __init__(self):
        """初始化故障诊断算法"""
        self.normal_data_stats = {}  # 正常状态数据统计
        self.thresholds = {}  # 阈值设置
        self.is_trained = False
        
    def set_normal_baseline(self, normal_features):
        """
        设置正常状态基线
        
        Args:
            normal_features (dict or list): 正常状态的特征数据
        """
        if isinstance(normal_features, dict):
            # 单个样本
            self.normal_data_stats = {
                'mean': normal_features,
                'std': {k: 0.1 * abs(v) if v != 0 else 0.1 for k, v in normal_features.items()},
                'count': 1
            }
        else:
            # 多个样本
            features_array = np.array([list(f.values()) for f in normal_features])
            feature_names = list(normal_features[0].keys())
            
            self.normal_data_stats = {
                'mean': dict(zip(feature_names, np.mean(features_array, axis=0))),
                'std': dict(zip(feature_names, np.std(features_array, axis=0))),
                'cov_matrix': np.cov(features_array.T),
                'feature_names': feature_names,
                'count': len(normal_features)
            }
        
        self.is_trained = True
        self._calculate_default_thresholds()
    
    def _calculate_default_thresholds(self):
        """计算默认阈值"""
        if not self.normal_data_stats:
            return
            
        # 基于3σ原则设置阈值
        for feature_name, mean_val in self.normal_data_stats['mean'].items():
            std_val = self.normal_data_stats['std'].get(feature_name, 0.1 * abs(mean_val))
            
            self.thresholds[feature_name] = {
                'warning_lower': mean_val - 2 * std_val,
                'warning_upper': mean_val + 2 * std_val,
                'fault_lower': mean_val - 3 * std_val,
                'fault_upper': mean_val + 3 * std_val
            }
    
    def set_custom_thresholds(self, feature_thresholds):
        """
        设置自定义阈值
        
        Args:
            feature_thresholds (dict): 特征阈值字典
        """
        self.thresholds.update(feature_thresholds)
    
    def diagnose(self, features, method='threshold'):
        """
        故障诊断
        
        Args:
            features (dict): 待诊断的特征数据
            method (str): 诊断方法，'threshold'或'mahalanobis'
            
        Returns:
            dict: 诊断结果
        """
        if not self.is_trained:
            return {
                'overall_status': 'error',
                'message': '诊断器未训练，请先设置正常状态基线',
                'details': {}
            }
        
        if method == 'threshold':
            return self._threshold_diagnosis(features)
        elif method == 'mahalanobis':
            return self._mahalanobis_diagnosis(features)
        else:
            raise ValueError(f"不支持的诊断方法: {method}")
    
    def _threshold_diagnosis(self, features):
        """
        阈值法故障诊断
        
        Args:
            features (dict): 特征数据
            
        Returns:
            dict: 诊断结果
        """
        results = {
            'method': 'threshold',
            'overall_status': 'normal',
            'fault_features': [],
            'warning_features': [],
            'normal_features': [],
            'details': {}
        }
        
        fault_count = 0
        warning_count = 0
        
        for feature_name, value in features.items():
            if feature_name not in self.thresholds:
                continue
                
            thresholds = self.thresholds[feature_name]
            status = 'normal'
            
            # 判断故障状态
            if (value < thresholds['fault_lower'] or 
                value > thresholds['fault_upper']):
                status = 'fault'
                fault_count += 1
                results['fault_features'].append(feature_name)
            elif (value < thresholds['warning_lower'] or 
                  value > thresholds['warning_upper']):
                status = 'warning'
                warning_count += 1
                results['warning_features'].append(feature_name)
            else:
                results['normal_features'].append(feature_name)
            
            results['details'][feature_name] = {
                'value': value,
                'status': status,
                'thresholds': thresholds,
                'deviation': self._calculate_deviation(value, feature_name)
            }
        
        # 确定整体状态
        if fault_count > 0:
            results['overall_status'] = 'fault'
        elif warning_count > 0:
            results['overall_status'] = 'warning'
        
        results['summary'] = {
            'total_features': len(features),
            'fault_count': fault_count,
            'warning_count': warning_count,
            'normal_count': len(results['normal_features'])
        }
        
        return results
    
    def _mahalanobis_diagnosis(self, features):
        """
        马氏距离故障诊断
        
        Args:
            features (dict): 特征数据
            
        Returns:
            dict: 诊断结果
        """
        if 'cov_matrix' not in self.normal_data_stats:
            return {
                'overall_status': 'error',
                'message': '马氏距离诊断需要多个正常样本来计算协方差矩阵',
                'details': {}
            }
        
        # 准备数据
        feature_names = self.normal_data_stats['feature_names']
        mean_vector = np.array([self.normal_data_stats['mean'][name] for name in feature_names])
        cov_matrix = self.normal_data_stats['cov_matrix']
        
        # 当前样本向量
        current_vector = np.array([features.get(name, 0) for name in feature_names])
        
        try:
            # 计算马氏距离
            cov_inv = inv(cov_matrix)
            mahal_distance = mahalanobis(current_vector, mean_vector, cov_inv)
            
            # 计算p值（基于卡方分布）
            p_value = 1 - chi2.cdf(mahal_distance**2, df=len(feature_names))
            
            # 判断状态
            if p_value < 0.01:
                status = 'fault'
            elif p_value < 0.05:
                status = 'warning'
            else:
                status = 'normal'
            
            # 计算特征贡献度
            diff_vector = current_vector - mean_vector
            contributions = np.abs(diff_vector * np.diag(cov_inv))
            contribution_percentages = contributions / np.sum(contributions) * 100
            
            results = {
                'method': 'mahalanobis',
                'overall_status': status,
                'mahalanobis_distance': mahal_distance,
                'p_value': p_value,
                'feature_contributions': dict(zip(feature_names, contribution_percentages)),
                'details': {
                    'mean_vector': mean_vector.tolist(),
                    'current_vector': current_vector.tolist(),
                    'difference_vector': diff_vector.tolist()
                }
            }
            
            return results
            
        except Exception as e:
            return {
                'overall_status': 'error',
                'message': f'马氏距离计算失败: {str(e)}',
                'details': {}
            }
    
    def _calculate_deviation(self, value, feature_name):
        """计算特征值偏离程度"""
        if feature_name not in self.normal_data_stats['mean']:
            return 0
            
        mean_val = self.normal_data_stats['mean'][feature_name]
        std_val = self.normal_data_stats['std'][feature_name]
        
        if std_val == 0:
            return 0
            
        return abs(value - mean_val) / std_val
    
    def get_diagnosis_summary(self, diagnosis_results):
        """
        获取诊断结果摘要
        
        Args:
            diagnosis_results (dict): 诊断结果
            
        Returns:
            str: 诊断摘要文本
        """
        if diagnosis_results['overall_status'] == 'error':
            return f"诊断错误: {diagnosis_results.get('message', '未知错误')}"
        
        method = diagnosis_results.get('method', 'unknown')
        status = diagnosis_results['overall_status']
        
        status_text = {
            'normal': '正常',
            'warning': '轻微异常',
            'fault': '故障'
        }.get(status, '未知')
        
        if method == 'threshold':
            summary = diagnosis_results.get('summary', {})
            return (f"阈值法诊断结果: {status_text}\n"
                   f"总特征数: {summary.get('total_features', 0)}\n"
                   f"故障特征: {summary.get('fault_count', 0)}\n"
                   f"警告特征: {summary.get('warning_count', 0)}")
        
        elif method == 'mahalanobis':
            distance = diagnosis_results.get('mahalanobis_distance', 0)
            p_value = diagnosis_results.get('p_value', 1)
            return (f"马氏距离诊断结果: {status_text}\n"
                   f"马氏距离: {distance:.4f}\n"
                   f"P值: {p_value:.6f}")
        
        return f"诊断结果: {status_text}"

    def multi_feature_mahalanobis_classification(self, features, positive_samples=None, negative_samples=None):
        """
        多特征马氏距离分类

        Args:
            features (dict): 当前特征数据
            positive_samples (list): 正样本特征列表
            negative_samples (list): 负样本特征列表

        Returns:
            dict: 分类结果
        """
        results = {
            'method': '多特征马氏距离分类',
            'features': features,
            'classification_result': 'unknown',
            'mahalanobis_distance_positive': float('inf'),
            'mahalanobis_distance_negative': float('inf'),
            'positive_stats': {},
            'negative_stats': {},
            'feature_space_analysis': {}
        }

        # 如果没有提供样本，使用默认的参考数据
        if positive_samples is None or negative_samples is None:
            positive_samples, negative_samples = self._generate_reference_samples(features)

        try:
            # 转换为数组格式
            feature_names = list(features.keys())
            current_sample = np.array([list(features.values())])

            # 正样本统计
            positive_array = np.array([list(sample.values()) for sample in positive_samples])
            positive_mean = np.mean(positive_array, axis=0)
            positive_std = np.std(positive_array, axis=0)
            positive_cov = np.cov(positive_array.T)

            # 负样本统计
            negative_array = np.array([list(sample.values()) for sample in negative_samples])
            negative_mean = np.mean(negative_array, axis=0)
            negative_std = np.std(negative_array, axis=0)
            negative_cov = np.cov(negative_array.T)

            # 存储统计信息
            results['positive_stats'] = {
                'mean': dict(zip(feature_names, positive_mean)),
                'count': len(positive_samples)
            }
            results['negative_stats'] = {
                'mean': dict(zip(feature_names, negative_mean)),
                'count': len(negative_samples)
            }

            # 构建特征空间分析数据（UI期望的格式）
            feature_space_analysis = {}
            for i, feature_name in enumerate(feature_names):
                feature_space_analysis[feature_name] = {
                    'current_value': float(current_sample[0][i]),
                    'positive_mean': float(positive_mean[i]),
                    'negative_mean': float(negative_mean[i]),
                    'positive_std': float(positive_std[i]),
                    'negative_std': float(negative_std[i])
                }
            results['feature_space_analysis'] = feature_space_analysis

            # 计算到正样本中心的马氏距离
            inv_positive_cov = np.linalg.inv(positive_cov)
            diff_positive = current_sample[0] - positive_mean
            mahal_dist_positive = np.sqrt(diff_positive.T @ inv_positive_cov @ diff_positive)

            # 计算到负样本中心的马氏距离
            inv_negative_cov = np.linalg.inv(negative_cov)
            diff_negative = current_sample[0] - negative_mean
            mahal_dist_negative = np.sqrt(diff_negative.T @ inv_negative_cov @ diff_negative)

            results['mahalanobis_distance_positive'] = float(mahal_dist_positive)
            results['mahalanobis_distance_negative'] = float(mahal_dist_negative)

            # 分类决策：距离哪个类别更近
            if mahal_dist_positive < mahal_dist_negative:
                results['classification_result'] = 'normal'
            else:
                results['classification_result'] = 'fault'

        except Exception as e:
            print(f"马氏距离计算失败: {e}")
            import traceback
            traceback.print_exc()
            results['classification_result'] = 'error'
            results['mahalanobis_distance_positive'] = float('inf')
            results['mahalanobis_distance_negative'] = float('inf')
            # 即使出错也要提供基本的feature_space_analysis结构
            if not results['feature_space_analysis']:
                for feature_name, value in features.items():
                    results['feature_space_analysis'][feature_name] = {
                        'current_value': float(value),
                        'positive_mean': 0.0,
                        'negative_mean': 0.0,
                        'positive_std': 1.0,
                        'negative_std': 1.0
                    }

        return results

    def single_feature_mahalanobis_threshold(self, features, positive_samples=None, negative_samples=None):
        """
        单特征马氏距离阈值计算

        Args:
            features (dict): 特征数据（应该只包含一个特征）
            positive_samples (list): 正样本特征列表
            negative_samples (list): 负样本特征列表

        Returns:
            dict: 诊断结果
        """
        feature_name = list(features.keys())[0]
        feature_value = list(features.values())[0]

        results = {
            'method': '单特征马氏距离阈值',
            'feature_name': feature_name,
            'feature_value': feature_value,
            'positive_stats': {},
            'negative_stats': {},
            'threshold': 0.0,
            'classification_result': 'unknown',
            'mahalanobis_distance': 0.0
        }

        # 如果没有提供样本，使用默认的参考数据
        if positive_samples is None or negative_samples is None:
            positive_samples, negative_samples = self._generate_reference_samples(features)

        # 提取单特征数据
        positive_values = [sample[feature_name] for sample in positive_samples]
        negative_values = [sample[feature_name] for sample in negative_samples]

        # 计算统计量
        positive_mean = np.mean(positive_values)
        positive_std = np.std(positive_values)
        negative_mean = np.mean(negative_values)
        negative_std = np.std(negative_values)

        results['positive_stats'] = {
            'mean': positive_mean,
            'std': positive_std,
            'count': len(positive_values)
        }
        results['negative_stats'] = {
            'mean': negative_mean,
            'std': negative_std,
            'count': len(negative_values)
        }

        # 基于马氏距离计算分类阈值
        # 阈值设定为两个分布中心点的中点
        threshold = (positive_mean + negative_mean) / 2
        results['threshold'] = threshold

        # 计算当前样本到正样本分布的马氏距离
        if positive_std > 0:
            mahal_distance = abs(feature_value - positive_mean) / positive_std
        else:
            mahal_distance = float('inf')

        results['mahalanobis_distance'] = mahal_distance

        # 分类决策
        if feature_value <= threshold:
            results['classification_result'] = 'normal'
        else:
            results['classification_result'] = 'fault'

        return results

    def generate_normal_reference_data(self, feature_names, n_samples=100):
        """
        生成正常状态参考数据

        Args:
            feature_names (list): 特征名称列表
            n_samples (int): 生成样本数量

        Returns:
            array: 参考数据数组
        """
        # 基于经验的正常状态参考值
        reference_ranges = {
            '均值': (0.0, 0.1),
            '均方根': (0.5, 1.5),
            '方差': (0.1, 1.0),
            '标准差': (0.3, 1.0),
            '偏度': (-0.5, 0.5),
            '峰度': (2.5, 4.0),
            '最大值': (1.0, 3.0),
            '最小值': (-3.0, -1.0),
            '峰峰值': (2.0, 6.0),
            '脉冲因子': (1.2, 1.8),
            '裕度因子': (1.3, 2.0),
            '波形因子': (1.1, 1.4),
            '峰值因子': (1.2, 1.8),
            '主频率': (10.0, 100.0),
            '频谱重心': (20.0, 80.0)
        }

        reference_data = []
        for _ in range(n_samples):
            sample = []
            for feature_name in feature_names:
                if feature_name in reference_ranges:
                    min_val, max_val = reference_ranges[feature_name]
                    value = np.random.uniform(min_val, max_val)
                else:
                    # 默认范围
                    value = np.random.uniform(0.0, 1.0)
                sample.append(value)
            reference_data.append(sample)

        return np.array(reference_data)

    def _generate_reference_samples(self, features):
        """
        生成参考样本用于分类

        Args:
            features (dict): 当前特征数据

        Returns:
            tuple: (正样本列表, 负样本列表)
        """
        feature_names = list(features.keys())

        # 生成正样本（正常状态）
        positive_samples = []
        for _ in range(50):
            sample = {}
            for feature_name in feature_names:
                # 正常状态的特征值范围
                if 'factor' in feature_name.lower() or 'ratio' in feature_name.lower():
                    # 因子类特征
                    sample[feature_name] = np.random.uniform(1.0, 2.0)
                elif 'frequency' in feature_name.lower() or '频率' in feature_name:
                    # 频率类特征
                    sample[feature_name] = np.random.uniform(10.0, 100.0)
                elif 'rms' in feature_name.lower() or '均方根' in feature_name:
                    # RMS类特征
                    sample[feature_name] = np.random.uniform(0.5, 1.5)
                else:
                    # 其他特征
                    sample[feature_name] = np.random.uniform(0.0, 1.0)
            positive_samples.append(sample)

        # 生成负样本（故障状态）
        negative_samples = []
        for _ in range(50):
            sample = {}
            for feature_name in feature_names:
                # 故障状态的特征值范围（通常更大）
                if 'factor' in feature_name.lower() or 'ratio' in feature_name.lower():
                    # 因子类特征
                    sample[feature_name] = np.random.uniform(3.0, 8.0)
                elif 'frequency' in feature_name.lower() or '频率' in feature_name:
                    # 频率类特征
                    sample[feature_name] = np.random.uniform(200.0, 500.0)
                elif 'rms' in feature_name.lower() or '均方根' in feature_name:
                    # RMS类特征
                    sample[feature_name] = np.random.uniform(3.0, 10.0)
                else:
                    # 其他特征
                    sample[feature_name] = np.random.uniform(2.0, 5.0)
            negative_samples.append(sample)

        return positive_samples, negative_samples

    def advanced_mahalanobis_diagnosis(self, features):
        """
        高级马氏距离故障诊断算法

        Args:
            features (dict): 特征数据

        Returns:
            dict: 诊断结果
        """
        results = {
            'method': '高级马氏距离',
            'features': features,
            'mahalanobis_distance': 0.0,
            'threshold': 0.0,
            'p_value': 0.0,
            'overall_status': 'normal',
            'feature_contributions': {}
        }

        # 将特征转换为数组
        feature_names = list(features.keys())
        feature_values = np.array(list(features.values())).reshape(1, -1)

        # 生成参考数据集
        normal_references = self.generate_normal_reference_data(feature_names, n_samples=100)

        # 计算协方差矩阵
        cov_matrix = np.cov(normal_references.T)

        # 计算均值
        mean_vector = np.mean(normal_references, axis=0)

        # 计算马氏距离
        try:
            # 确保协方差矩阵可逆
            if np.linalg.det(cov_matrix) == 0:
                # 如果协方差矩阵奇异，添加小的正则化项
                cov_matrix += np.eye(cov_matrix.shape[0]) * 1e-6

            inv_cov_matrix = np.linalg.inv(cov_matrix)
            diff = feature_values[0] - mean_vector
            mahal_dist = np.sqrt(diff.T @ inv_cov_matrix @ diff)

            results['mahalanobis_distance'] = float(mahal_dist)

            # 计算阈值（基于卡方分布）
            dof = len(feature_names)  # 自由度
            confidence_level = 0.95  # 95%置信度
            threshold = np.sqrt(chi2.ppf(confidence_level, dof))
            results['threshold'] = float(threshold)

            # 计算p值
            p_value = 1 - chi2.cdf(mahal_dist**2, dof)
            results['p_value'] = float(p_value)

            # 确定状态
            if mahal_dist <= threshold:
                results['overall_status'] = 'normal'
            elif mahal_dist <= threshold * 1.5:
                results['overall_status'] = 'warning'
            else:
                results['overall_status'] = 'fault'

            # 计算各特征的贡献度
            for i, feature_name in enumerate(feature_names):
                contribution = abs(diff[i] * inv_cov_matrix[i, i])
                results['feature_contributions'][feature_name] = float(contribution)

        except Exception as e:
            print(f"马氏距离计算失败: {e}")
            results['mahalanobis_distance'] = float('inf')
            results['threshold'] = 0.0
            results['p_value'] = 0.0
            results['overall_status'] = 'error'

        return results

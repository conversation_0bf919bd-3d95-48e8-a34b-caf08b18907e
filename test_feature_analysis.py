#!/usr/bin/env python3
"""
特征提取与分析功能测试脚本
"""

import sys
import os
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.feature_analysis import FeatureAnalysis
from database.db_manager import DatabaseManager


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("特征提取与分析功能测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建数据库管理器（可能连接失败，但不影响界面测试）
        try:
            self.db_manager = DatabaseManager()
        except:
            self.db_manager = None
            print("警告: 数据库连接失败，但界面功能仍可测试")
        
        # 创建特征分析界面
        self.feature_analysis = FeatureAnalysis(self.db_manager)
        self.setCentralWidget(self.feature_analysis)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式"""
        from ui.styles import get_main_stylesheet
        self.setStyleSheet(get_main_stylesheet())


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("特征提取与分析测试")
    app.setApplicationVersion("1.0")
    
    # 创建并显示测试窗口
    window = TestWindow()
    window.show()
    
    print("特征提取与分析功能测试启动")
    print("功能说明:")
    print("1. 点击'选择TDMS文件'按钮选择TDMS文件")
    print("2. 选择要分析的通道")
    print("3. 设置采样率")
    print("4. 选择要执行的分析方法")
    print("5. 点击'开始分析'执行分析")
    print("6. 查看分析结果并可保存图片")
    print()
    print("注意: 如果没有TDMS文件，可以使用date1文件夹中的示例文件进行测试")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

# EMD（经验模态分解）模块重写总结

## 🎯 重写目标

基于提供的参考文件`经验模态分解EMD代码示例和可视化.ipynb`，完全重写特征提取中的本征模函数（EMD）模块，实现更专业、更准确的EMD分析功能。

## 🔧 技术实现

### 1. 核心EMD算法实现

#### `_perform_emd(data, max_imfs=8)`
- **功能**: 执行完整的EMD分解过程
- **算法流程**:
  1. 初始化残差为原始信号
  2. 迭代提取IMF分量
  3. 检查单调性和能量阈值
  4. 返回所有IMF分量和最终残差

#### `_extract_imf(data, max_iterations=50)`
- **功能**: 提取单个IMF分量
- **筛选过程**:
  1. 找到局部极值点
  2. 构造上下包络线
  3. 计算均值包络
  4. 更新信号并检查停止条件

#### `_find_extrema(data)`
- **功能**: 检测局部极值点
- **方法**: 使用`scipy.signal.argrelextrema`
- **优化**: 自动包含边界点，确保包络完整性

#### `_create_envelope(data, extrema_indices)`
- **功能**: 创建平滑包络线
- **方法**: 
  - 优先使用三次样条插值
  - 失败时回退到线性插值
  - 支持边界外推

### 2. 可视化功能

#### `analyze_emd(data, ax)`
- **原始信号**: 灰色半透明显示
- **IMF分量**: 不同颜色，垂直偏移显示
- **信息显示**: 分量数量统计
- **颜色方案**: 
  - IMF1: #FF6B6B (红色)
  - IMF2: #4ECDC4 (青色)
  - IMF3: #45B7D1 (蓝色)
  - IMF4: #96CEB4 (绿色)

#### `analyze_emd_hilbert(data, ax)` (新增)
- **希尔伯特变换**: 计算瞬时频率和幅值
- **时频分析**: 散点图显示频率变化
- **颜色映射**: 频率用颜色表示
- **点大小**: 幅值用点大小表示

## 📊 算法特性

### 停止条件
1. **标准差阈值**: SD < 0.2
2. **单调性检查**: 检测信号是否变为单调
3. **能量阈值**: 残差能量 < 1% 原始信号能量
4. **最大迭代次数**: 防止无限循环

### 质量控制
1. **极值点验证**: 确保足够的极值点数量
2. **包络有效性**: 检查包络构造是否成功
3. **边界处理**: 正确处理信号边界
4. **数值稳定性**: 避免除零和溢出

## 🧪 测试验证

### 测试信号
```python
# 复合信号测试
signal1 = sin(2π × 5Hz × t)      # 低频分量
signal2 = 0.5 × sin(2π × 20Hz × t)  # 中频分量  
signal3 = 0.3 × sin(2π × 50Hz × t)  # 高频分量
noise = 0.1 × random_noise       # 噪声
```

### 测试结果
- ✅ **分解成功**: 提取9个IMF分量
- ✅ **能量分布合理**: 各分量能量递减
- ✅ **极值点检测**: 正确识别极大值和极小值
- ✅ **可视化效果**: 清晰的分量显示

## 🆚 与原版本对比

| 特性 | 原版本 | 新版本 |
|------|--------|--------|
| 算法实现 | 简单带通滤波器 | 完整EMD算法 |
| 依赖库 | PyEMD (外部) | 内置实现 |
| 分量数量 | 固定1个 | 自适应多个 |
| 可视化 | 单一波形 | 多分量偏移显示 |
| 时频分析 | 无 | 希尔伯特变换 |
| 错误处理 | 基础 | 完善的异常处理 |

## 🔬 算法优势

### 1. 自适应性
- 根据信号特性自动确定IMF数量
- 智能停止条件避免过分解
- 适应不同类型的振动信号

### 2. 鲁棒性
- 完善的边界处理
- 多重插值方法备选
- 数值稳定性保证

### 3. 专业性
- 符合EMD理论标准
- 支持希尔伯特变换分析
- 适用于轴承故障诊断

### 4. 可视化
- 直观的分量显示
- 时频特性分析
- 专业的配色方案

## 📈 应用场景

### 轴承故障诊断
1. **早期故障检测**: 提取微弱故障特征
2. **故障模式识别**: 分离不同频率成分
3. **趋势分析**: 监测故障发展过程

### 振动信号分析
1. **多分量分离**: 分离复合振动信号
2. **频率识别**: 识别主要频率成分
3. **噪声抑制**: 分离有用信号和噪声

## 🚀 使用方法

### 基础EMD分析
```python
analyzer = AnalysisMethods(sample_rate=1000)
fig, ax = plt.subplots()
analyzer.analyze_emd(signal_data, ax)
```

### 高级时频分析
```python
analyzer = AnalysisMethods(sample_rate=1000)
fig, ax = plt.subplots()
analyzer.analyze_emd_hilbert(signal_data, ax)
```

## 📝 技术细节

### 包络构造
- **三次样条插值**: 提供平滑的包络线
- **边界外推**: 处理信号端点
- **线性备选**: 插值失败时的备选方案

### 极值点检测
- **scipy.signal.argrelextrema**: 专业的极值检测
- **边界包含**: 确保端点被正确处理
- **阶数控制**: order=1 检测相邻点极值

### 停止条件
- **相对标准差**: SD = Σ(h_old - h_new)² / Σh_old²
- **阈值设定**: 默认0.2，可调节
- **单调性**: 检查信号是否失去振荡特性

## ✅ 验证通过

1. ✅ **算法正确性**: 符合EMD理论
2. ✅ **数值稳定性**: 无异常或崩溃
3. ✅ **可视化效果**: 清晰专业的显示
4. ✅ **性能表现**: 合理的计算时间
5. ✅ **错误处理**: 完善的异常捕获

EMD模块重写完成，现在提供了专业级的经验模态分解功能，完全满足轴承故障诊断的需求！

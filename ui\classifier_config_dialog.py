"""
分类器配置对话框
包含特征选择和分类器参数配置功能
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QGroupBox, QCheckBox, QFormLayout, QSpinBox,
    QDoubleSpinBox, QSlider, QDialogButtonBox, QTabWidget, QWidget,
    QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)


class ClassifierConfigDialog(QDialog):
    """分类器配置对话框"""
    
    config_changed = pyqtSignal(dict)  # 配置变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("分类器配置")
        self.setModal(True)
        self.resize(800, 600)
        
        # 分类器选项
        self.classifier_options = {
            "SVM": {"C": 1.0, "gamma": "scale", "kernel": "rbf"},
            "Random Forest": {"n_estimators": 100, "max_depth": None, "random_state": 42},
            "KNN": {"n_neighbors": 5, "weights": "uniform"},
            "Naive Bayes": {},
            "Decision Tree": {"max_depth": None, "random_state": 42}
        }
        
        self.feature_checkboxes = {}
        self.param_controls = {}
        
        self.init_ui()
        self.apply_styles()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("分类器配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 特征选择选项卡
        self.create_feature_selection_tab(tab_widget)
        
        # 分类器配置选项卡
        self.create_classifier_config_tab(tab_widget)
        
        # 训练配置选项卡
        self.create_training_config_tab(tab_widget)
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self.apply_config)
        layout.addWidget(button_box)
        
    def create_feature_selection_tab(self, parent):
        """创建特征选择选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 特征类型选择
        feature_group = QGroupBox("特征类型选择")
        feature_layout = QGridLayout(feature_group)
        
        feature_types = [
            ("时域有量纲特征", "包含均值、方差、峰值等统计特征"),
            ("时域无量纲特征", "包含峰值因子、脉冲因子等无量纲特征"),
            ("频域特征", "包含频谱重心、频谱方差等频域特征"),
            ("时频域特征", "包含小波包能量、时频分布特征"),
            ("小波特征", "基于小波变换的多尺度特征"),
            ("EMD特征", "基于经验模态分解的本征模态函数特征")
        ]
        
        for i, (feature_type, description) in enumerate(feature_types):
            checkbox = QCheckBox(feature_type)
            checkbox.setChecked(True)
            checkbox.setToolTip(description)
            self.feature_checkboxes[feature_type] = checkbox
            
            row = i // 2
            col = i % 2
            feature_layout.addWidget(checkbox, row, col)
        
        layout.addWidget(feature_group)
        
        # 特征预处理选项
        preprocess_group = QGroupBox("特征预处理")
        preprocess_layout = QVBoxLayout(preprocess_group)
        
        self.normalize_checkbox = QCheckBox("标准化处理")
        self.normalize_checkbox.setChecked(True)
        self.normalize_checkbox.setToolTip("对特征进行零均值单位方差标准化")
        preprocess_layout.addWidget(self.normalize_checkbox)
        
        self.pca_checkbox = QCheckBox("主成分分析降维")
        self.pca_checkbox.setToolTip("使用PCA进行特征降维")
        preprocess_layout.addWidget(self.pca_checkbox)
        
        # PCA组件数设置
        pca_layout = QHBoxLayout()
        pca_label = QLabel("主成分数量:")
        self.pca_components_spin = QSpinBox()
        self.pca_components_spin.setRange(2, 50)
        self.pca_components_spin.setValue(10)
        self.pca_components_spin.setEnabled(False)
        
        self.pca_checkbox.toggled.connect(self.pca_components_spin.setEnabled)
        
        pca_layout.addWidget(pca_label)
        pca_layout.addWidget(self.pca_components_spin)
        pca_layout.addStretch()
        preprocess_layout.addLayout(pca_layout)
        
        layout.addWidget(preprocess_group)
        layout.addStretch()
        
        parent.addTab(tab, "特征选择")
        
    def create_classifier_config_tab(self, parent):
        """创建分类器配置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 分类器选择
        classifier_group = QGroupBox("分类器类型")
        classifier_layout = QFormLayout(classifier_group)
        
        self.classifier_combo = QComboBox()
        self.classifier_combo.addItems(list(self.classifier_options.keys()))
        self.classifier_combo.currentTextChanged.connect(self.update_parameters)
        classifier_layout.addRow("算法:", self.classifier_combo)
        
        layout.addWidget(classifier_group)
        
        # 参数配置区域（滚动）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.param_widget = QWidget()
        self.param_layout = QFormLayout(self.param_widget)
        scroll_area.setWidget(self.param_widget)
        
        param_group = QGroupBox("参数配置")
        param_group_layout = QVBoxLayout(param_group)
        param_group_layout.addWidget(scroll_area)
        
        layout.addWidget(param_group)
        
        parent.addTab(tab, "分类器配置")
        
        # 初始化参数界面
        self.update_parameters()
        
    def create_training_config_tab(self, parent):
        """创建训练配置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 数据分割配置
        split_group = QGroupBox("数据分割")
        split_layout = QFormLayout(split_group)
        
        # 测试集比例
        self.test_size_slider = QSlider(Qt.Horizontal)
        self.test_size_slider.setRange(10, 50)
        self.test_size_slider.setValue(20)
        self.test_size_slider.valueChanged.connect(self.update_test_size_label)
        
        self.test_size_label = QLabel("20%")
        test_size_layout = QHBoxLayout()
        test_size_layout.addWidget(self.test_size_slider)
        test_size_layout.addWidget(self.test_size_label)
        split_layout.addRow("测试集比例:", test_size_layout)
        
        # 随机种子
        self.random_seed_spin = QSpinBox()
        self.random_seed_spin.setRange(0, 9999)
        self.random_seed_spin.setValue(42)
        split_layout.addRow("随机种子:", self.random_seed_spin)
        
        layout.addWidget(split_group)
        
        # 交叉验证配置
        cv_group = QGroupBox("交叉验证")
        cv_layout = QFormLayout(cv_group)
        
        self.cv_checkbox = QCheckBox("启用交叉验证")
        self.cv_checkbox.setChecked(True)
        cv_layout.addRow(self.cv_checkbox)
        
        self.cv_folds_spin = QSpinBox()
        self.cv_folds_spin.setRange(3, 10)
        self.cv_folds_spin.setValue(5)
        cv_layout.addRow("折数:", self.cv_folds_spin)
        
        layout.addWidget(cv_group)
        layout.addStretch()
        
        parent.addTab(tab, "训练配置")
        
    def update_parameters(self):
        """更新参数配置界面"""
        # 清除现有参数控件
        while self.param_layout.count():
            child = self.param_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        classifier_type = self.classifier_combo.currentText()
        params = self.classifier_options[classifier_type]

        self.param_controls = {}

        for param_name, default_value in params.items():
            if isinstance(default_value, int):
                control = QSpinBox()
                control.setRange(1, 1000)
                control.setValue(default_value)
            elif isinstance(default_value, float):
                control = QDoubleSpinBox()
                control.setRange(0.001, 100.0)
                control.setDecimals(3)
                control.setValue(default_value)
            elif isinstance(default_value, str):
                control = QComboBox()
                if param_name == "kernel":
                    control.addItems(["rbf", "linear", "poly", "sigmoid"])
                elif param_name == "gamma":
                    control.addItems(["scale", "auto"])
                elif param_name == "weights":
                    control.addItems(["uniform", "distance"])
                control.setCurrentText(default_value)
            else:
                control = QLineEdit(str(default_value))

            self.param_controls[param_name] = control
            self.param_layout.addRow(f"{param_name}:", control)
            
    def update_test_size_label(self):
        """更新测试集比例标签"""
        value = self.test_size_slider.value()
        self.test_size_label.setText(f"{value}%")
        
    def get_config(self):
        """获取当前配置"""
        config = {
            'selected_features': [],
            'classifier_type': self.classifier_combo.currentText(),
            'classifier_params': {},
            'test_size': self.test_size_slider.value() / 100.0,
            'random_seed': self.random_seed_spin.value(),
            'use_cv': self.cv_checkbox.isChecked(),
            'cv_folds': self.cv_folds_spin.value(),
            'normalize': self.normalize_checkbox.isChecked(),
            'use_pca': self.pca_checkbox.isChecked(),
            'pca_components': self.pca_components_spin.value()
        }
        
        # 获取选中的特征
        for feature_type, checkbox in self.feature_checkboxes.items():
            if checkbox.isChecked():
                config['selected_features'].append(feature_type)
                
        # 获取分类器参数 - 转换为网格搜索格式
        classifier_type = self.classifier_combo.currentText()
        for param_name, control in self.param_controls.items():
            if isinstance(control, QSpinBox):
                value = control.value()
                # 为网格搜索创建参数范围
                if classifier_type == "SVM" and param_name == "C":
                    config['classifier_params'][param_name] = [value/10, value, value*10]
                elif classifier_type == "Random Forest" and param_name == "n_estimators":
                    config['classifier_params'][param_name] = [max(50, value-50), value, value+50]
                elif classifier_type == "KNN" and param_name == "n_neighbors":
                    config['classifier_params'][param_name] = [max(1, value-2), value, value+2]
                else:
                    config['classifier_params'][param_name] = [value]
            elif isinstance(control, QDoubleSpinBox):
                value = control.value()
                # 为网格搜索创建参数范围
                if classifier_type == "SVM" and param_name == "C":
                    config['classifier_params'][param_name] = [value/10, value, value*10]
                else:
                    config['classifier_params'][param_name] = [value]
            elif isinstance(control, QComboBox):
                value = control.currentText()
                # 对于字符串参数，保持单个值
                config['classifier_params'][param_name] = [value]
            elif isinstance(control, QLineEdit):
                text = control.text()
                try:
                    if '.' in text:
                        value = float(text)
                        config['classifier_params'][param_name] = [value]
                    else:
                        value = int(text)
                        config['classifier_params'][param_name] = [value]
                except ValueError:
                    config['classifier_params'][param_name] = [text]
                    
        return config
        
    def apply_config(self):
        """应用配置"""
        config = self.get_config()
        self.config_changed.emit(config)
        
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
            }}
            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                border: 2px solid {SECONDARY_BG};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            QCheckBox {{
                font-size: 14px;
                color: {TEXT_PRIMARY};
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QComboBox, QSpinBox, QDoubleSpinBox {{
                font-size: 14px;
                padding: 8px;
                border: 2px solid {SECONDARY_BG};
                border-radius: 6px;
                background-color: {SECONDARY_BG};
                color: {TEXT_PRIMARY};
            }}
            QSlider::groove:horizontal {{
                border: 1px solid {SECONDARY_BG};
                height: 8px;
                background: {SECONDARY_BG};
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }}
            QPushButton {{
                font-size: 14px;
                padding: 10px 20px;
                border-radius: 6px;
                border: none;
                background-color: {ACCENT_COLOR};
                color: white;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
        """)

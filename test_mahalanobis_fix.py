#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试马氏距离分类修复效果
验证多特征马氏距离分类是否能正确返回feature_space_analysis字段
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_multi_feature_mahalanobis():
    """测试多特征马氏距离分类"""
    print("=" * 60)
    print("测试多特征马氏距离分类")
    print("=" * 60)
    
    try:
        # 导入算法接口
        from algorithms import get_algorithm_interface
        
        # 创建算法接口
        interface = get_algorithm_interface(sample_rate=1000)
        
        # 生成测试特征数据（选择6个特征）
        test_features = {
            '均值': 0.05,
            '标准差': 0.8,
            '方差': 0.64,
            '峰值因子': 1.5,
            '偏度': 0.2,
            '峰度': 3.2
        }
        
        print(f"测试特征: {list(test_features.keys())}")
        print(f"特征值: {list(test_features.values())}")
        
        # 执行多特征马氏距离分类
        results = interface.diagnose_fault(test_features, 'multi_mahalanobis')
        
        print(f"\n✓ 多特征马氏距离分类执行成功")
        print(f"✓ 分类结果: {results.get('classification_result', 'unknown')}")
        print(f"✓ 到正样本距离: {results.get('mahalanobis_distance_positive', 'N/A')}")
        print(f"✓ 到负样本距离: {results.get('mahalanobis_distance_negative', 'N/A')}")
        
        # 检查是否包含feature_space_analysis字段
        if 'feature_space_analysis' in results:
            print(f"✓ 包含feature_space_analysis字段")
            
            feature_analysis = results['feature_space_analysis']
            print(f"✓ 特征空间分析包含 {len(feature_analysis)} 个特征")
            
            # 验证每个特征的分析数据
            for feature_name, analysis in feature_analysis.items():
                required_fields = ['current_value', 'positive_mean', 'negative_mean', 'positive_std', 'negative_std']
                missing_fields = [field for field in required_fields if field not in analysis]
                
                if missing_fields:
                    print(f"⚠️  特征 {feature_name} 缺少字段: {missing_fields}")
                else:
                    print(f"✓ 特征 {feature_name} 数据完整")
            
            print("\n特征空间分析详情:")
            for feature_name, analysis in feature_analysis.items():
                print(f"  {feature_name}:")
                print(f"    当前值: {analysis.get('current_value', 'N/A'):.6f}")
                print(f"    正样本均值: {analysis.get('positive_mean', 'N/A'):.6f}")
                print(f"    负样本均值: {analysis.get('negative_mean', 'N/A'):.6f}")
                print(f"    正样本标准差: {analysis.get('positive_std', 'N/A'):.6f}")
                print(f"    负样本标准差: {analysis.get('negative_std', 'N/A'):.6f}")
            
            return True
        else:
            print("❌ 缺少feature_space_analysis字段")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_feature_mahalanobis():
    """测试单特征马氏距离阈值"""
    print("\n" + "=" * 60)
    print("测试单特征马氏距离阈值")
    print("=" * 60)
    
    try:
        # 导入算法接口
        from algorithms import get_algorithm_interface
        
        # 创建算法接口
        interface = get_algorithm_interface(sample_rate=1000)
        
        # 生成测试特征数据（只选择1个特征）
        test_features = {
            '均值': 0.05
        }
        
        print(f"测试特征: {list(test_features.keys())}")
        print(f"特征值: {list(test_features.values())}")
        
        # 执行单特征马氏距离阈值计算
        results = interface.diagnose_fault(test_features, 'single_mahalanobis')
        
        print(f"\n✓ 单特征马氏距离阈值计算执行成功")
        print(f"✓ 分类结果: {results.get('classification_result', 'unknown')}")
        print(f"✓ 马氏距离: {results.get('mahalanobis_distance', 'N/A')}")
        print(f"✓ 分类阈值: {results.get('threshold', 'N/A')}")
        print(f"✓ 特征名称: {results.get('feature_name', 'N/A')}")
        print(f"✓ 特征值: {results.get('feature_value', 'N/A')}")
        
        # 检查统计信息
        if 'positive_stats' in results and 'negative_stats' in results:
            print(f"✓ 包含正负样本统计信息")
            print(f"  正样本统计: 均值={results['positive_stats'].get('mean', 'N/A'):.6f}, "
                  f"标准差={results['positive_stats'].get('std', 'N/A'):.6f}")
            print(f"  负样本统计: 均值={results['negative_stats'].get('mean', 'N/A'):.6f}, "
                  f"标准差={results['negative_stats'].get('std', 'N/A'):.6f}")
            return True
        else:
            print("⚠️  缺少统计信息")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理")
    print("=" * 60)
    
    try:
        # 导入算法接口
        from algorithms import get_algorithm_interface
        
        # 创建算法接口
        interface = get_algorithm_interface(sample_rate=1000)
        
        # 测试空特征数据
        empty_features = {}
        
        print("测试空特征数据...")
        results = interface.diagnose_fault(empty_features, 'multi_mahalanobis')
        
        if 'feature_space_analysis' in results:
            print("✓ 即使特征为空，也返回了feature_space_analysis字段")
        else:
            print("⚠️  空特征时未返回feature_space_analysis字段")
        
        # 测试无效特征数据
        invalid_features = {
            '无效特征1': float('nan'),
            '无效特征2': float('inf')
        }
        
        print("测试无效特征数据...")
        results = interface.diagnose_fault(invalid_features, 'multi_mahalanobis')
        
        if 'feature_space_analysis' in results:
            print("✓ 即使特征无效，也返回了feature_space_analysis字段")
            return True
        else:
            print("⚠️  无效特征时未返回feature_space_analysis字段")
            return False
            
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("马氏距离分类修复验证测试")
    print("=" * 60)
    
    # 测试1: 多特征马氏距离分类
    test1_result = test_multi_feature_mahalanobis()
    
    # 测试2: 单特征马氏距离阈值
    test2_result = test_single_feature_mahalanobis()
    
    # 测试3: 错误处理
    test3_result = test_error_handling()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed_tests = sum([test1_result, test2_result, test3_result])
    total_tests = 3
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！马氏距离分类修复成功！")
        print("✓ 多特征马氏距离分类正常工作")
        print("✓ 单特征马氏距离阈值正常工作")
        print("✓ 错误处理机制正常")
        return True
    else:
        print(f"❌ {total_tests - passed_tests} 个测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

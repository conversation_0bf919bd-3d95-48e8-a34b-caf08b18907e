"""
数据库管理模块
负责数据库连接、TDMS文件路径查询等功能
"""

import pymysql
import configparser
import os
from typing import List, Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        初始化数据库管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        self.connection = None
        self.is_connected = False
        
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = pymysql.connect(
                host=self.config.get('DATABASE', 'host'),
                port=self.config.getint('DATABASE', 'port'),
                user=self.config.get('DATABASE', 'user'),
                password=self.config.get('DATABASE', 'password'),
                database=self.config.get('DATABASE', 'database'),
                charset=self.config.get('DATABASE', 'charset'),
                autocommit=True
            )
            self.is_connected = True
            logger.info("数据库连接成功")
            return True
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.is_connected = False
            logger.info("数据库连接已断开")
    
    def test_connection(self) -> Tuple[bool, str]:
        """
        测试数据库连接
        
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            if self.connect():
                cursor = self.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                return True, "数据库连接测试成功"
            else:
                return False, "无法连接到数据库"
        except Exception as e:
            return False, f"数据库连接测试失败: {e}"
    
    def get_tdms_files(self) -> List[Dict]:
        """
        获取所有TDMS文件信息
        
        Returns:
            List[Dict]: TDMS文件信息列表
        """
        if not self.is_connected:
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 根据您提供的表结构查询TDMS文件
            query = """
            SELECT
                ID,
                测试时间,
                车型,
                部件,
                传感器类型,
                传感器编号,
                TDMS文件路径,
                TDMS文件名,
                CONCAT(TDMS文件路径, '\\\\', TDMS文件名, '.tdms') as 完整路径
            FROM tdms
            ORDER BY 测试时间 DESC
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            
            logger.info(f"获取到 {len(results)} 个TDMS文件")
            return results
            
        except Exception as e:
            logger.error(f"查询TDMS文件失败: {e}")
            return []
    
    def get_file_by_id(self, file_id: int) -> Optional[Dict]:
        """
        根据ID获取特定TDMS文件信息
        
        Args:
            file_id: 文件ID
            
        Returns:
            Optional[Dict]: 文件信息
        """
        if not self.is_connected:
            if not self.connect():
                return None
        
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            query = """
            SELECT
                ID,
                测试时间,
                车型,
                部件,
                传感器类型,
                传感器编号,
                TDMS文件路径,
                TDMS文件名,
                CONCAT(TDMS文件路径, '\\\\', TDMS文件名, '.tdms') as 完整路径
            FROM tdms
            WHERE ID = %s
            """
            
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            cursor.close()
            
            return result
            
        except Exception as e:
            logger.error(f"查询文件失败: {e}")
            return None
    
    def get_distinct_values(self, column_name: str) -> List[str]:
        """
        获取指定列的不重复值列表

        Args:
            column_name: 列名

        Returns:
            List[str]: 不重复值列表
        """
        if not self.is_connected:
            if not self.connect():
                return []

        try:
            cursor = self.connection.cursor()
            query = f"SELECT DISTINCT {column_name} FROM tdms WHERE {column_name} IS NOT NULL AND {column_name} != '' ORDER BY {column_name}"
            cursor.execute(query)
            results = cursor.fetchall()
            return [row[0] for row in results if row[0]]

        except Exception as e:
            logger.error(f"查询不重复值失败: {e}")
            return []

    def get_test_dates(self) -> List[str]:
        """
        获取所有测试时间列表

        Returns:
            List[str]: 测试时间列表
        """
        return self.get_distinct_values('测试时间')

    def get_components(self) -> List[str]:
        """
        获取所有部件列表

        Returns:
            List[str]: 部件列表
        """
        return self.get_distinct_values('部件')

    def search_files(self, **kwargs) -> List[Dict]:
        """
        搜索TDMS文件

        Args:
            **kwargs: 搜索条件 (车型, 部件, 传感器类型等)

        Returns:
            List[Dict]: 匹配的文件列表
        """
        if not self.is_connected:
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 构建查询条件
            conditions = []
            params = []
            
            for key, value in kwargs.items():
                if value and value.strip():
                    conditions.append(f"{key} LIKE %s")
                    params.append(f"%{value}%")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            query = f"""
            SELECT
                ID,
                测试时间,
                车型,
                部件,
                传感器类型,
                传感器编号,
                TDMS文件路径,
                TDMS文件名,
                CONCAT(TDMS文件路径, '\\\\', TDMS文件名, '.tdms') as 完整路径
            FROM tdms
            WHERE {where_clause}
            ORDER BY 测试时间 DESC
            """
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            cursor.close()
            
            return results
            
        except Exception as e:
            logger.error(f"搜索文件失败: {e}")
            return []
    
    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.disconnect()

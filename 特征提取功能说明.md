# 特征提取与分析功能说明

## 功能概述

特征提取与分析模块是地面数据分析决策系统的核心功能之一，基于date1文件夹中的tdms_analyzer.py实现，提供了13种专业的信号分析方法，用于轴承故障诊断和振动信号分析。

## 支持的分析方法

### 时域特征分析
1. **时域有量纲特征值**
   - 均值、均方根、方差、标准差
   - 偏度、峰度、最大值、最小值、峰峰值
   - 脉冲因子、裕度因子、波形因子、峰值因子

2. **时域无量纲特征值**
   - 偏度、峰度、波形因子、峰值因子
   - 脉冲因子、裕度因子、方差因子

3. **自相关函数**
   - 分析信号的周期性特征
   - 检测信号中的重复模式

4. **互相关函数**
   - 分析两个信号之间的相关性
   - 检测信号延迟和相似性

### 频域特征分析
5. **频谱分析**
   - 快速傅里叶变换(FFT)频谱
   - 识别信号的频率成分

6. **倒频谱**
   - 用于检测周期性故障
   - 齿轮箱和轴承故障诊断

7. **包络谱**
   - 用于轴承故障诊断
   - 检测调制信号

8. **阶比谱**
   - 用于旋转机械分析
   - 消除转速变化影响

9. **功率谱**
   - 功率谱密度分析
   - 信号能量分布

### 时频域特征分析
10. **短时傅里叶变换(STFT)**
    - 时频分析
    - 观察频率随时间的变化

11. **魏格纳威尔分布(WVD)**
    - 高分辨率时频分析
    - 更精确的时频表示

12. **小波变换**
    - 多分辨率时频分析
    - 适合非平稳信号分析

13. **本征模函数(IMF)**
    - 经验模态分解
    - 信号自适应分解

## 使用方法

### 1. 文件选择
- 点击"选择TDMS文件"按钮选择要分析的TDMS文件
- 或者从文件选择器页面选择文件，系统会自动传递到特征分析模块
- 支持的文件格式：.tdms

### 2. 通道选择
- 从下拉菜单中选择要分析的通道
- 系统会自动识别TDMS文件中的所有可用通道

### 3. 参数设置
- 设置采样率（默认1000Hz）
- 采样率影响频域分析的准确性

### 4. 分析方法选择
- 勾选要执行的分析方法
- 可以同时选择多种方法进行批量分析
- 使用"全选"和"清除"按钮快速操作

### 5. 执行分析
- 点击"开始分析"按钮
- 系统会在后台线程中执行分析，避免界面卡顿
- 进度条显示分析进度

### 6. 查看结果
- 分析结果显示在右侧面板中
- 每种方法生成一个独立的图表
- 支持滚动查看所有结果

### 7. 保存结果
- 点击"保存图片"按钮保存分析结果
- 支持PNG和PDF格式
- 多个结果会自动合并为一个文件

## 技术特点

### 界面设计
- 现代化的PyQt5界面
- 符合系统整体设计风格
- 响应式布局，适应不同屏幕尺寸

### 性能优化
- 后台线程处理，避免界面冻结
- 进度指示和状态反馈
- 内存优化，支持大文件分析

### 错误处理
- 完善的错误检测和提示
- 文件格式验证
- 数据有效性检查
- 用户友好的错误信息

### 集成特性
- 与文件选择器无缝集成
- 支持数据库文件信息显示
- 统一的样式和交互体验

## 测试方法

### 使用测试脚本
```bash
python test_feature_analysis.py
```

### 使用示例数据
- date1文件夹中包含示例TDMS文件
- 可用于功能测试和验证

### 完整系统测试
```bash
python main.py
```
然后导航到"特征提取与分析"页面

## 依赖要求

- PyQt5 >= 5.15.0
- numpy >= 1.20.0
- matplotlib >= 3.5.0
- scipy >= 1.7.0
- nptdms >= 1.4.0
- PyWavelets >= 1.3.0

## 注意事项

1. **文件格式**: 仅支持TDMS格式文件
2. **内存使用**: 大文件分析可能需要较多内存
3. **计算时间**: 复杂分析方法（如小波变换）可能需要较长时间
4. **采样率**: 确保设置正确的采样率以获得准确的频域分析结果
5. **数据质量**: 确保TDMS文件完整且通道数据有效

## 故障排除

### 常见问题
1. **文件加载失败**: 检查文件路径和权限
2. **分析失败**: 检查数据有效性和采样率设置
3. **内存不足**: 尝试分析较小的数据段
4. **显示异常**: 检查matplotlib和PyQt5版本兼容性

### 获取帮助
- 查看系统日志文件
- 检查错误提示信息
- 验证依赖包版本

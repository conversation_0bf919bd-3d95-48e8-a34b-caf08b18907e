#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试马氏距离对比图表修复效果
专门验证图表是否能正确显示
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_chart_fix():
    """测试图表修复效果"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("马氏距离图表修复测试")
    main_window.resize(450, 300)
    
    # 设置窗口样式
    main_window.setStyleSheet("""
        QMainWindow {
            background-color: #1e1e2e;
            color: white;
            font-family: 'Microsoft YaHei';
        }
    """)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(15)
    layout.setContentsMargins(20, 20, 20, 20)
    
    # 标题
    title_label = QLabel("马氏距离图表修复测试")
    title_label.setStyleSheet("""
        QLabel {
            font-size: 16px;
            font-weight: bold;
            color: #6c5ce7;
            margin-bottom: 10px;
        }
    """)
    title_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(title_label)
    
    # 说明文字
    info_label = QLabel("""
    图表修复内容：
    ✓ 改用垂直条形图（更易调试）
    ✓ 使用更明亮的颜色对比
    ✓ 设置测试数据确保显示
    ✓ 增加边框和网格
    ✓ 优化matplotlib配置
    
    预期效果：
    • 显示绿色和红色的条形图
    • 显示具体的数值标签
    • 有清晰的标题和坐标轴
    """)
    info_label.setStyleSheet("""
        QLabel {
            font-size: 10px;
            color: #a0a0a0;
            background-color: #2d2d3d;
            padding: 10px;
            border-radius: 5px;
            line-height: 1.3;
        }
    """)
    info_label.setWordWrap(True)
    layout.addWidget(info_label)
    
    # 创建测试按钮
    test_btn = QPushButton("测试修复后的图表")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c5ce7;
            color: white;
            font-size: 12px;
            font-weight: 500;
            min-height: 40px;
            padding: 10px 15px;
            border-radius: 6px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
        QPushButton:hover {
            background-color: #5a4fcf;
        }
        QPushButton:pressed {
            background-color: #4834d4;
        }
    """)
    
    # 结果显示区域
    result_label = QLabel("点击按钮测试图表修复效果...")
    result_label.setStyleSheet("""
        QLabel {
            font-size: 10px;
            color: #00d4aa;
            background-color: #2d2d3d;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #4d4d6d;
        }
    """)
    result_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(result_label)
    
    def open_dialog():
        """打开马氏距离分类子界面"""
        try:
            result_label.setText("正在测试图表修复...")
            app.processEvents()
            
            print("=" * 60)
            print("测试马氏距离图表修复效果")
            print("=" * 60)
            
            from ui.fault_diagnosis import MultiFeatureMahalanobisDialog
            
            # 创建测试特征数据
            test_features = {
                '均方根': 1.750195,
                '偏度': -0.043924,
                '峰度': -1.245920,
                '峰值因子': 1.938262,
                '裕度因子': 2.530399,
                '脉冲因子': 2.246699
            }
            
            print("测试数据:")
            for name, value in test_features.items():
                print(f"  {name}: {value}")
            
            result_label.setText("子界面已打开，请检查图表显示...")
            app.processEvents()
            
            # 创建并显示子界面
            dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                result_label.setText("✓ 测试完成")
                
                print("=" * 60)
                print("测试结果:")
                print("请检查子界面中的马氏距离对比图表是否显示：")
                print("1. 是否有绿色和红色的条形图？")
                print("2. 是否显示了数值标签？")
                print("3. 是否有清晰的标题和坐标轴？")
                print("4. 图表是否不再是空白？")
                print("=" * 60)
                
                results = dialog.get_results()
                if results:
                    pos_dist = results.get('mahalanobis_distance_positive', 'N/A')
                    neg_dist = results.get('mahalanobis_distance_negative', 'N/A')
                    print(f"正样本距离: {pos_dist}")
                    print(f"负样本距离: {neg_dist}")
                    
                    result_text = f"✓ 图表修复测试完成\n正样本: {pos_dist}\n负样本: {neg_dist}"
                    result_label.setText(result_text)
                else:
                    result_label.setText("⚠️ 未获取到距离数据")
            else:
                result_label.setText("❌ 测试被取消")
                print("测试被用户取消")
                
        except Exception as e:
            error_msg = f"❌ 测试失败: {str(e)}"
            result_label.setText(error_msg)
            print(error_msg)
            print("详细错误信息:")
            import traceback
            traceback.print_exc()
    
    test_btn.clicked.connect(open_dialog)
    layout.addWidget(test_btn)
    layout.addWidget(result_label)
    
    layout.addStretch()
    
    # 显示主窗口
    main_window.show()
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    print("马氏距离图表修复测试")
    print("=" * 60)
    print("修复内容:")
    print("1. 改用垂直条形图替代水平条形图")
    print("2. 使用更明亮的颜色 (#00ff88, #ff4444)")
    print("3. 设置测试数据 (0.8337, 12.8174)")
    print("4. 增加边框、网格和数值标签")
    print("5. 优化matplotlib配置和字体设置")
    print("6. 增加详细的调试输出")
    print("=" * 60)
    print("预期效果:")
    print("- 马氏距离对比图表应该显示两个条形")
    print("- 绿色条形表示正样本距离")
    print("- 红色条形表示负样本距离")
    print("- 每个条形上方显示具体数值")
    print("=" * 60)
    
    try:
        exit_code = test_chart_fix()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

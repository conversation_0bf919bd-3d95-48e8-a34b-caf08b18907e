#!/usr/bin/env python3
"""
故障异常报警页面改进演示
展示主要的UI/UX改进效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QHBoxLayout, QLabel, QFrame, QPushButton, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import get_main_stylesheet, TEXT_PRIMARY, ACCENT_COLOR


class ImprovementDemo(QMainWindow):
    """改进演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("故障异常报警页面改进演示")
        self.setGeometry(50, 50, 1600, 1000)
        
        # 设置样式
        self.setStyleSheet(get_main_stylesheet())
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 添加标题
        title = QLabel("🎨 故障异常报警页面改进演示")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            margin: 20px 0px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 2px solid {ACCENT_COLOR};
        """)
        title.setAlignment(Qt.AlignCenter)
        scroll_layout.addWidget(title)
        
        # 添加改进说明
        improvements_text = """
        <h3>🔧 主要改进内容：</h3>
        <ul>
            <li><b>卡片式设计：</b>增加阴影效果和圆角，提升视觉层次感</li>
            <li><b>间距优化：</b>拉宽信息块间距，避免数据项过于紧凑</li>
            <li><b>状态信息重组：</b>单独置于页面上方，减少与下方数据的干扰</li>
            <li><b>颜色统一：</b>统一状态颜色饱和度，减少刺眼的蓝色高亮</li>
            <li><b>字体层级：</b>标题加粗加大，数据值突出显示，次要文字减小</li>
            <li><b>图标辅助：</b>为各数据项添加直观的图标识别</li>
            <li><b>进度条美化：</b>渐变绿-黄-红设计，圆角效果</li>
            <li><b>按钮统一：</b>主按钮蓝色，次按钮灰蓝，危险按钮红色，增加间距</li>
        </ul>
        """
        
        info_label = QLabel(improvements_text)
        info_label.setStyleSheet(f"""
            font-size: 14px;
            color: {TEXT_PRIMARY};
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            margin: 10px 0px;
        """)
        info_label.setWordWrap(True)
        scroll_layout.addWidget(info_label)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"""
            background-color: {ACCENT_COLOR};
            height: 3px;
            margin: 20px 0px;
        """)
        scroll_layout.addWidget(separator)
        
        # 添加改进后的故障报警系统
        system_title = QLabel("📊 改进后的故障异常报警系统")
        system_title.setStyleSheet(f"""
            font-size: 22px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin: 15px 0px;
            padding: 10px;
        """)
        system_title.setAlignment(Qt.AlignCenter)
        scroll_layout.addWidget(system_title)
        
        # 创建故障报警系统实例
        self.fault_alarm = FaultAlarmSystem()
        scroll_layout.addWidget(self.fault_alarm)
        
        # 添加底部说明
        footer_text = """
        <p><b>💡 使用说明：</b></p>
        <p>• 页面会自动模拟数据更新，每5秒刷新一次状态</p>
        <p>• 点击各个按钮可以体验交互效果</p>
        <p>• 观察卡片悬停效果和按钮反馈</p>
        <p>• 注意颜色搭配和视觉层次的改进</p>
        """
        
        footer_label = QLabel(footer_text)
        footer_label.setStyleSheet(f"""
            font-size: 13px;
            color: {TEXT_PRIMARY};
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid {ACCENT_COLOR};
            margin: 20px 0px;
        """)
        footer_label.setWordWrap(True)
        scroll_layout.addWidget(footer_label)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.setCentralWidget(scroll_area)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("故障异常报警页面改进演示")
    app.setApplicationVersion("2.0")
    
    # 创建并显示演示窗口
    demo = ImprovementDemo()
    demo.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

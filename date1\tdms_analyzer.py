import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from nptdms import TdmsFile
from scipy import signal
from scipy.fft import fft, fftfreq, ifft
from scipy.stats import kurtosis, skew
import pywt
from scipy.signal import hilbert
import os
import threading
from datetime import datetime
from PIL import Image, ImageTk
from matplotlib.backends.backend_agg import FigureCanvasAgg

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 1. 设置更大的字体
LARGE_FONT = ("SimHei", 20)
MEDIUM_FONT = ("SimHei", 16)

# 2. 设置舒适的颜色方案
BACKGROUND_COLOR = "#F0F4F8"  # 浅蓝灰色背景
FRAME_COLOR = "#E1E8ED"       # 淡蓝框架
TEXT_COLOR = "#333333"        # 深灰文本
BUTTON_COLOR = "#4A90E2"      # 蓝色按钮
BUTTON_TEXT_COLOR = "white"   # 按钮文字白色
ACCENT_COLOR = "#5CB85C"      # 绿色强调

class TDMSAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("TDMS文件时频分析器")
        self.root.geometry("1600x900")
        self.root.configure(bg=BACKGROUND_COLOR)
        
        # 添加窗口大小变化事件处理
        self.root.bind("<Configure>", self.on_window_resize)
        
        # 设置样式
        self.setup_styles()
        
        # 数据存储
        self.tdms_data = None
        self.channels = []
        self.selected_channel = None
        self.sample_rate = 1000  # 默认采样率
        self.analysis_results = []  # 存储分析结果
        
        self.setup_ui()
        
    def setup_styles(self):
        """设置自定义样式"""
        style = ttk.Style()
        style.theme_use('clam')  # 使用clam主题作为基础
        
        # 设置整体背景
        style.configure("TFrame", background=BACKGROUND_COLOR)
        style.configure("TLabel", background=BACKGROUND_COLOR, foreground=TEXT_COLOR, font=MEDIUM_FONT)
        style.configure("TLabelframe", background=BACKGROUND_COLOR, foreground=TEXT_COLOR, font=MEDIUM_FONT)
        style.configure("TLabelframe.Label", background=BACKGROUND_COLOR, foreground=TEXT_COLOR, font=MEDIUM_FONT)
        
        # 设置按钮样式
        style.configure("TButton", 
                      background=BUTTON_COLOR, 
                      foreground=BUTTON_TEXT_COLOR,
                      font=MEDIUM_FONT,
                      padding=10)
        
        # 按钮悬停效果
        style.map("TButton",
                background=[('active', ACCENT_COLOR)],
                foreground=[('active', "white")])
        
        # 设置组合框样式
        style.configure("TCombobox", 
                      fieldbackground=FRAME_COLOR,
                      background=FRAME_COLOR,
                      foreground=TEXT_COLOR,
                      arrowcolor=BUTTON_COLOR,
                      font=MEDIUM_FONT)
        
        # 设置输入框样式
        style.configure("TEntry", 
                      fieldbackground=FRAME_COLOR,
                      foreground=TEXT_COLOR,
                      font=MEDIUM_FONT)
                      
        # 设置复选框样式
        style.configure("TCheckbutton", 
                      background=BACKGROUND_COLOR,
                      foreground=TEXT_COLOR,
                      font=MEDIUM_FONT)
        
        # 设置进度条样式
        style.configure("TProgressbar", 
                      background=ACCENT_COLOR,
                      troughcolor=FRAME_COLOR)
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.root.option_add("*Font", MEDIUM_FONT)  # 全局字体
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=3)  # 给结果区域更多空间
        main_frame.rowconfigure(2, weight=1)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        
        ttk.Button(file_frame, text="选择TDMS文件", command=self.load_tdms_file).pack(side=tk.LEFT, padx=(0, 15))
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT)
        
        # 通道选择区域
        channel_frame = ttk.LabelFrame(main_frame, text="通道选择", padding="10")
        channel_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        
        ttk.Label(channel_frame, text="选择通道:").pack(side=tk.LEFT, padx=(0, 15))
        self.channel_var = tk.StringVar()
        self.channel_combo = ttk.Combobox(channel_frame, textvariable=self.channel_var, state="readonly", font=MEDIUM_FONT)
        self.channel_combo.pack(side=tk.LEFT, padx=(0, 15))
        self.channel_combo.bind('<<ComboboxSelected>>', self.on_channel_selected)
        
        ttk.Label(channel_frame, text="采样率(Hz):").pack(side=tk.LEFT, padx=(15, 5))
        self.sample_rate_var = tk.StringVar(value="1000")
        self.sample_rate_entry = ttk.Entry(channel_frame, textvariable=self.sample_rate_var, width=10)
        self.sample_rate_entry.pack(side=tk.LEFT)
        
        # 分析方法选择区域
        analysis_frame = ttk.LabelFrame(main_frame, text="分析方法", padding="10")
        analysis_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 创建滚动框架
        canvas = tk.Canvas(analysis_frame, height=400, bg=BACKGROUND_COLOR, highlightthickness=0)
        scrollbar = ttk.Scrollbar(analysis_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 分析方法列表 - 使用复选框
        self.analysis_methods = [
            "时域有量纲特征值",
            "时域无量纲特征值", 
            "自相关函数",
            "互相关函数",
            "频谱分析",
            "倒频谱",
            "包络谱",
            "阶比谱",
            "功率谱",
            "短时傅里叶变换",
            "魏格纳威尔分布",
            "小波变换",
            "本征模函数"
        ]
        
        self.analysis_vars = []
        for i, method in enumerate(self.analysis_methods):
            var = tk.BooleanVar()
            self.analysis_vars.append(var)
            cb = tk.Checkbutton(scrollable_frame, text=method, variable=var, font=MEDIUM_FONT, anchor=tk.W, 
                               bg=BACKGROUND_COLOR, fg=TEXT_COLOR, activebackground=BACKGROUND_COLOR,
                               selectcolor=FRAME_COLOR)
            cb.pack(anchor=tk.W, pady=8, padx=5, fill=tk.X)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 按钮区域
        button_frame = ttk.Frame(analysis_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="全选", command=self.select_all_methods).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清除选择", command=self.clear_selection).pack(side=tk.LEFT)
        
        # 分析按钮
        ttk.Button(main_frame, text="开始分析", command=self.start_analysis, style="TButton").grid(row=3, column=0, columnspan=2, pady=15)
        
        # 结果显示区域（可滚动）
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        result_frame.columnconfigure(0, weight=1)  # 让内容能够水平扩展
        result_frame.rowconfigure(1, weight=1)  # 让内容能够垂直扩展

        # 保存图片和清除结果按钮（上移到顶部）
        save_frame = ttk.Frame(result_frame)
        save_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Button(save_frame, text="保存图片", command=self.save_figure).pack(side=tk.LEFT)
        ttk.Button(save_frame, text="清除结果", command=self.clear_results).pack(side=tk.LEFT, padx=(10, 0))

        self.result_canvas = tk.Canvas(result_frame, height=700, bg=BACKGROUND_COLOR, highlightthickness=0)
        self.result_scrollbar_y = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_canvas.yview)
        self.result_scrollbar_x = ttk.Scrollbar(result_frame, orient="horizontal", command=self.result_canvas.xview)
        self.result_inner_frame = ttk.Frame(self.result_canvas)

        self.result_inner_frame.bind(
            "<Configure>",
            lambda e: self.result_canvas.configure(scrollregion=self.result_canvas.bbox("all"))
        )
        self.result_canvas.create_window((0, 0), window=self.result_inner_frame, anchor="nw")
        self.result_canvas.configure(yscrollcommand=self.result_scrollbar_y.set, xscrollcommand=self.result_scrollbar_x.set)

        self.result_canvas.pack(side="left", fill="both", expand=True)
        self.result_scrollbar_y.pack(side="right", fill="y")
        self.result_scrollbar_x.pack(side="bottom", fill="x")
        
        # 鼠标滚轮支持滚动
        def _on_mousewheel(event):
            self.result_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        self.result_canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def load_tdms_file(self):
        """加载TDMS文件"""
        file_path = filedialog.askopenfilename(
            title="选择TDMS文件",
            filetypes=[("TDMS files", "*.tdms"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                self.file_label.config(text=os.path.basename(file_path))
                # 使用正确的API读取TDMS文件
                self.tdms_data = TdmsFile.read(file_path)
                
                # 获取所有通道
                self.channels = []
                for group in self.tdms_data.groups():
                    for channel in group.channels():
                        self.channels.append(channel.name)
                
                # 更新通道选择下拉框
                self.channel_combo['values'] = self.channels
                if self.channels:
                    self.channel_combo.set(self.channels[0])
                    self.selected_channel = self.channels[0]
                    
                messagebox.showinfo("成功", f"成功加载TDMS文件，发现 {len(self.channels)} 个通道")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载文件失败: {str(e)}")
    
    def on_channel_selected(self, event):
        """通道选择事件"""
        self.selected_channel = self.channel_var.get()
    
    def select_all_methods(self):
        """选择所有分析方法"""
        for var in self.analysis_vars:
            var.set(True)
    
    def clear_selection(self):
        """清除选择"""
        for var in self.analysis_vars:
            var.set(False)
    
    def get_selected_data(self):
        """获取选中的通道数据"""
        if not self.tdms_data or not self.selected_channel:
            return None
            
        for group in self.tdms_data.groups():
            for channel in group.channels():
                if channel.name == self.selected_channel:
                    return channel[:]
        return None
    
    def start_analysis(self):
        """开始分析"""
        if not self.tdms_data or not self.selected_channel:
            messagebox.showwarning("警告", "请先选择TDMS文件和通道")
            return
            
        # 获取选中的分析方法
        selected_methods = []
        for i, var in enumerate(self.analysis_vars):
            if var.get():
                selected_methods.append(i)
        
        if not selected_methods:
            messagebox.showwarning("警告", "请选择至少一种分析方法")
            return
            
        # 获取采样率
        try:
            self.sample_rate = float(self.sample_rate_var.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的采样率")
            return
            
        # 在新线程中运行分析
        self.progress.start()
        thread = threading.Thread(target=self.run_analysis, args=(selected_methods,))
        thread.daemon = True
        thread.start()
    
    def run_analysis(self, selected_methods):
        """运行分析"""
        try:
            data = self.get_selected_data()
            if data is None:
                self.root.after(0, lambda: messagebox.showerror("错误", "无法获取通道数据"))
                return
            
            # 清除之前的结果
            self.analysis_results = []
            
            figs = []  # 每个分析方法一个fig
            # 执行选中的分析方法
            for method_idx in selected_methods:
                # 设置图表样式
                plt.rcParams['figure.facecolor'] = BACKGROUND_COLOR
                plt.rcParams['axes.facecolor'] = FRAME_COLOR
                plt.rcParams['axes.edgecolor'] = TEXT_COLOR
                plt.rcParams['axes.labelcolor'] = TEXT_COLOR
                plt.rcParams['xtick.color'] = TEXT_COLOR
                plt.rcParams['ytick.color'] = TEXT_COLOR
                plt.rcParams['text.color'] = TEXT_COLOR
                plt.rcParams['axes.grid'] = True
                plt.rcParams['grid.alpha'] = 0.3
                plt.rcParams['font.size'] = 16
                
                # 使用稍微窄一点的图形，避免右侧被截断
                fig, ax = plt.subplots(figsize=(10, 5))
                method_name = self.analysis_methods[method_idx]
                try:
                    if method_idx == 0:
                        self.analyze_time_domain_features(data, ax, "dimensional")
                    elif method_idx == 1:
                        self.analyze_time_domain_features(data, ax, "dimensionless")
                    elif method_idx == 2:
                        self.analyze_autocorrelation(data, ax)
                    elif method_idx == 3:
                        self.analyze_crosscorrelation(data, ax)
                    elif method_idx == 4:
                        self.analyze_spectrum(data, ax)
                    elif method_idx == 5:
                        self.analyze_cepstrum(data, ax)
                    elif method_idx == 6:
                        self.analyze_envelope_spectrum(data, ax)
                    elif method_idx == 7:
                        self.analyze_order_spectrum(data, ax)
                    elif method_idx == 8:
                        self.analyze_power_spectrum(data, ax)
                    elif method_idx == 9:
                        self.analyze_stft(data, ax)
                    elif method_idx == 10:
                        self.analyze_wigner_ville(data, ax)
                    elif method_idx == 11:
                        self.analyze_wavelet(data, ax)
                    elif method_idx == 12:
                        self.analyze_emd(data, ax)
                    ax.set_title(method_name, fontsize=22, fontweight='bold')
                    # 增加更多的右边距，确保标签不会被裁剪
                    fig.tight_layout(rect=[0, 0, 0.95, 0.95])
                except Exception as e:
                    ax.text(0.5, 0.5, f'分析失败:\n{str(e)}', 
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=18, color='red')
                    ax.set_title(method_name, fontsize=22, fontweight='bold')
                    fig.tight_layout(rect=[0, 0, 0.95, 0.95])
                
                figs.append(fig)
            # 在主线程中更新显示
            self.root.after(0, lambda: self.display_results_in_panel(figs))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析过程中出现错误: {str(e)}"))
        finally:
            self.root.after(0, self.progress.stop)
    
    def display_results_in_panel(self, figs):
        """在面板中显示结果（每个fig单独显示，支持滚动+缩放）"""
        # 清除之前的图形
        for widget in self.result_inner_frame.winfo_children():
            widget.destroy()
        self.current_figs = figs  # 保存当前图形以便保存
        self.tk_images = []  # 防止图片被垃圾回收

        max_width = 0
        total_height = 0
        pil_images = []

        # 先将所有fig渲染为PIL图片，统计最大宽度和总高度
        for fig in figs:
            canvas = FigureCanvasAgg(fig)
            canvas.draw()
            w, h = canvas.get_width_height()
            img = np.frombuffer(canvas.tostring_rgb(), dtype='uint8').reshape(h, w, 3)
            pil_img = Image.fromarray(img)
            pil_images.append(pil_img)
            max_width = max(max_width, w)
            total_height += h

        # 获取结果面板可用宽度，考虑滚动条宽度
        result_frame_width = self.result_canvas.winfo_width() - self.result_scrollbar_y.winfo_width() - 20
        
        # 如果图片过大，自动缩放
        scale = 1.0
        if max_width > result_frame_width and result_frame_width > 100:  # 确保面板已经实际渲染
            scale = result_frame_width / max_width

        y_offset = 0
        for i, pil_img in enumerate(pil_images):
            if scale != 1.0:
                w, h = pil_img.size
                new_width = int(w * scale)
                new_height = int(h * scale)
                try:
                    pil_img = pil_img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                except AttributeError:
                    # 对于旧版PIL，LANCZOS可能不可用，使用ANTIALIAS
                    pil_img = pil_img.resize((new_width, new_height), Image.ANTIALIAS)
                    
            tk_img = ImageTk.PhotoImage(pil_img)
            label = tk.Label(self.result_inner_frame, image=tk_img, bg=BACKGROUND_COLOR)
            label.pack(anchor="center", pady=10, fill=tk.X)
            self.tk_images.append(tk_img)
            y_offset += pil_img.size[1]

        # 设置Canvas的scrollregion，确保横向也能滚动
        self.result_canvas.update_idletasks()
        bbox = self.result_canvas.bbox("all")
        if bbox:
            self.result_canvas.config(scrollregion=bbox)
        
        messagebox.showinfo("完成", "分析完成！结果已显示在面板中。")
    
    def save_figure(self):
        """保存所有分析结果为一张大图"""
        if hasattr(self, 'current_figs') and self.current_figs:
            try:
                import matplotlib as mpl
                from matplotlib.backends.backend_agg import FigureCanvasAgg
                import numpy as np
                # 先将所有fig渲染为图片
                images = []
                for fig in self.current_figs:
                    canvas = FigureCanvasAgg(fig)
                    canvas.draw()
                    w, h = canvas.get_width_height()
                    img = np.frombuffer(canvas.tostring_rgb(), dtype='uint8').reshape(h, w, 3)
                    images.append(img)
                # 纵向拼接
                total_height = sum(img.shape[0] for img in images)
                max_width = max(img.shape[1] for img in images)
                big_img = np.ones((total_height, max_width, 3), dtype='uint8') * 255
                y = 0
                for img in images:
                    h, w, _ = img.shape
                    big_img[y:y+h, :w, :] = img
                    y += h
                # 保存
                from PIL import Image
                im = Image.fromarray(big_img)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tdms_analysis_{timestamp}.png"
                im.save(filename)
                messagebox.showinfo("成功", f"图片已保存为: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存图片失败: {str(e)}")
        else:
            messagebox.showwarning("警告", "没有可保存的分析结果")
    
    def clear_results(self):
        """清除所有分析结果"""
        for widget in self.result_inner_frame.winfo_children():
            widget.destroy()
        if hasattr(self, 'current_figs'):
            delattr(self, 'current_figs')
    
    def analyze_time_domain_features(self, data, ax, feature_type):
        """时域特征分析（严格按表格内容）"""
        if feature_type == "dimensional":
            features = {
                '均值': np.mean(data),
                '均方根': np.sqrt(np.mean(data**2)),
                '方差': np.var(data),
                '标准差': np.std(data),
                '偏度': skew(data),
                '峰度': kurtosis(data),
                '最大值': np.max(data),
                '最小值': np.min(data),
                '峰峰值': np.max(data) - np.min(data),
                '脉冲因子': np.max(np.abs(data)) / np.mean(np.abs(data)),
                '裕度因子': np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2,
                '波形因子': np.sqrt(np.mean(data**2)) / np.mean(np.abs(data)),
                '峰值因子': np.max(np.abs(data)) / np.sqrt(np.mean(data**2)),
            }
        else:  # dimensionless
            features = {
                '偏度': skew(data),
                '峰度': kurtosis(data),
                '波形因子': np.sqrt(np.mean(data**2)) / np.mean(np.abs(data)),
                '峰值因子': np.max(np.abs(data)) / np.sqrt(np.mean(data**2)),
                '脉冲因子': np.max(np.abs(data)) / np.mean(np.abs(data)),
                '裕度因子': np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2,
                '方差因子': np.var(data) / np.mean(data)**2 if np.mean(data) != 0 else 0
            }
        
        # 绘制特征值
        names = list(features.keys())
        values = list(features.values())
        
        bars = ax.bar(names, values, color=BUTTON_COLOR, alpha=0.7)
        ax.set_title(f'时域{feature_type}特征值', fontsize=22, fontweight='bold')
        ax.set_ylabel('数值', fontsize=18)
        ax.set_xlabel('特征类型', fontsize=18)
        ax.tick_params(axis='x', rotation=45, labelsize=16)
        ax.tick_params(axis='y', labelsize=16)
        ax.grid(True, alpha=0.3)
        
        # 在柱状图上添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=14)
        
        # 缩小Y轴范围，防止数值太大导致无法看清图表
        if max(values) > 0:
            ax.set_ylim(top=max(values) * 1.1)
    
    def analyze_autocorrelation(self, data, ax):
        """自相关函数分析"""
        # 计算自相关函数
        autocorr = signal.correlate(data, data, mode='full')
        autocorr = autocorr[len(data)-1:]
        
        # 归一化
        autocorr = autocorr / autocorr[0]
        
        time = np.arange(len(autocorr)) / self.sample_rate
        ax.plot(time, autocorr, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('自相关函数', fontsize=22, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=18)
        ax.set_ylabel('自相关系数', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_crosscorrelation(self, data, ax):
        """互相关函数分析（这里用数据与其延迟版本的相关）"""
        # 创建延迟版本
        delay = len(data) // 4
        delayed_data = np.roll(data, delay)
        
        # 计算互相关函数
        crosscorr = signal.correlate(data, delayed_data, mode='full')
        crosscorr = crosscorr[len(data)-1:]
        
        # 归一化
        crosscorr = crosscorr / np.sqrt(np.sum(data**2) * np.sum(delayed_data**2))
        
        time = np.arange(len(crosscorr)) / self.sample_rate
        ax.plot(time, crosscorr, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('互相关函数', fontsize=22, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=18)
        ax.set_ylabel('互相关系数', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_spectrum(self, data, ax):
        """频谱分析"""
        # 计算FFT
        fft_data = fft(data)
        freqs = fftfreq(len(data), 1/self.sample_rate)
        
        # 只显示正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft_data[:len(freqs)//2])
        
        ax.plot(positive_freqs, positive_fft, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('频谱分析', fontsize=22, fontweight='bold')
        ax.set_xlabel('频率 (赫兹)', fontsize=18)
        ax.set_ylabel('幅值', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_cepstrum(self, data, ax):
        """倒频谱分析"""
        # 计算倒频谱
        fft_data = fft(data)
        log_spectrum = np.log(np.abs(fft_data) + 1e-10)
        cepstrum = np.abs(ifft(log_spectrum))
        
        time = np.arange(len(cepstrum)) / self.sample_rate
        ax.plot(time[:len(time)//2], cepstrum[:len(cepstrum)//2], color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('倒频谱', fontsize=22, fontweight='bold')
        ax.set_xlabel('倒频率 (秒)', fontsize=18)
        ax.set_ylabel('幅值', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_envelope_spectrum(self, data, ax):
        """包络谱分析"""
        # 计算解析信号
        analytic_signal = hilbert(data)
        envelope = np.abs(analytic_signal)
        
        # 计算包络的频谱
        fft_envelope = fft(envelope)
        freqs = fftfreq(len(envelope), 1/self.sample_rate)
        
        # 只显示正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft_envelope[:len(freqs)//2])
        
        ax.plot(positive_freqs, positive_fft, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('包络谱', fontsize=22, fontweight='bold')
        ax.set_xlabel('频率 (赫兹)', fontsize=18)
        ax.set_ylabel('幅值', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_order_spectrum(self, data, ax):
        """阶比谱分析（简化版本）"""
        # 假设转速为60Hz（3600rpm）
        rpm = 3600
        order_freq = rpm / 60  # Hz
        
        # 计算阶比
        freqs = fftfreq(len(data), 1/self.sample_rate)
        orders = freqs / order_freq
        
        fft_data = fft(data)
        fft_magnitude = np.abs(fft_data)
        
        # 只显示正阶比部分
        positive_orders = orders[:len(orders)//2]
        positive_fft = fft_magnitude[:len(orders)//2]
        
        ax.plot(positive_orders, positive_fft, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('阶比谱', fontsize=22, fontweight='bold')
        ax.set_xlabel('阶比', fontsize=18)
        ax.set_ylabel('幅值', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_power_spectrum(self, data, ax):
        """功率谱分析"""
        freqs, psd = signal.welch(data, self.sample_rate, nperseg=1024)
        
        ax.semilogy(freqs, psd, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title('功率谱密度', fontsize=22, fontweight='bold')
        ax.set_xlabel('频率 (赫兹)', fontsize=18)
        ax.set_ylabel('功率谱密度 (分贝/赫兹)', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)
    
    def analyze_stft(self, data, ax):
        """短时傅里叶变换"""
        # 计算STFT
        f, t, Zxx = signal.stft(data, self.sample_rate, nperseg=256, noverlap=128)
        
        # 绘制时频图 - 使用更舒适的颜色映射
        im = ax.pcolormesh(t, f, np.abs(Zxx), shading='gouraud', cmap='viridis')
        ax.set_title('短时傅里叶变换', fontsize=22, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=18)
        ax.set_ylabel('频率 (赫兹)', fontsize=18)
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('幅值', fontsize=16)
        cbar.ax.tick_params(labelsize=14)
        ax.tick_params(labelsize=16)
    
    def analyze_wigner_ville(self, data, ax):
        """魏格纳威尔分布（简化版本）"""
        # 由于完整的WVD计算复杂，这里用STFT的平方作为近似
        f, t, Zxx = signal.stft(data, self.sample_rate, nperseg=256, noverlap=128)
        
        # 计算WVD近似
        wvd = np.abs(Zxx)**2
        
        im = ax.pcolormesh(t, f, wvd, shading='gouraud', cmap='plasma')
        ax.set_title('魏格纳威尔分布', fontsize=22, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=18)
        ax.set_ylabel('频率 (赫兹)', fontsize=18)
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('功率', fontsize=16)
        ax.tick_params(labelsize=16)
    
    def analyze_wavelet(self, data, ax):
        """小波变换"""
        try:
            # 询问用户选择小波变换模式
            import tkinter.simpledialog as simpledialog
            
            # 在后台线程中无法直接显示对话框，使用默认的快速模式
            # 如果需要用户选择，可以在主线程中预先设置
            
            # 使用快速模式（默认）
            max_length = 5000  # 限制最大数据长度
            num_scales = 16    # 使用较少的尺度
            
            if len(data) > max_length:
                data_subset = data[:max_length]
                print(f"数据过长，使用前{max_length}个采样点进行小波变换")
            else:
                data_subset = data
            
            # 使用对数分布的尺度，减少计算量
            scales = np.logspace(0, 2, num_scales)  # 从1到100的num_scales个尺度
            wavelet = 'cmor1.5-1.0'  # 复Morlet小波
            
            # 计算小波变换
            coefficients, frequencies = pywt.cwt(data_subset, scales, wavelet, 1/self.sample_rate)
            
            # 绘制小波变换结果
            time = np.arange(len(data_subset)) / self.sample_rate
            im = ax.pcolormesh(time, frequencies, np.abs(coefficients), shading='gouraud', cmap='jet')
            ax.set_title('小波变换 (快速模式)', fontsize=22, fontweight='bold')
            ax.set_xlabel('时间 (秒)', fontsize=18)
            ax.set_ylabel('频率 (赫兹)', fontsize=18)
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('幅值', fontsize=16)
            ax.tick_params(labelsize=16)
            
        except Exception as e:
            # 如果小波变换失败，尝试使用STFT作为替代
            try:
                print(f"小波变换失败，使用STFT替代: {e}")
                self.analyze_wavelet_alternative(data, ax)
            except Exception as e2:
                # 如果替代方法也失败，显示错误信息
                ax.text(0.5, 0.5, f'小波变换失败: {str(e)}', 
                       ha='center', va='center', transform=ax.transAxes, fontsize=16)
                ax.set_title('小波变换 (失败)', fontsize=22, fontweight='bold')
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
    
    def analyze_wavelet_alternative(self, data, ax):
        """小波变换的替代方法（使用STFT）"""
        # 限制数据长度
        max_length = 10000
        if len(data) > max_length:
            data_subset = data[:max_length]
        else:
            data_subset = data
        
        # 使用STFT作为替代
        f, t, Zxx = signal.stft(data_subset, self.sample_rate, nperseg=256, noverlap=128)
        
        # 计算功率谱密度
        power_spectrum = np.abs(Zxx)**2
        
        # 绘制结果
        im = ax.pcolormesh(t, f, power_spectrum, shading='gouraud', cmap='hot')
        ax.set_title('小波变换 (STFT替代)', fontsize=22, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=18)
        ax.set_ylabel('频率 (赫兹)', fontsize=18)
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('功率', fontsize=16)
        ax.tick_params(labelsize=16)
    
    def analyze_emd(self, data, ax):
        """本征模函数分析（简化版本）"""
        # 由于EMD需要专门的库，这里用简单的滤波来模拟
        # 实际应用中建议使用PyEMD库
        
        # 使用带通滤波器模拟IMF
        low_freq = 10  # Hz
        high_freq = 50  # Hz
        
        b, a = signal.butter(4, [low_freq, high_freq], btype='band', fs=self.sample_rate)
        imf = signal.filtfilt(b, a, data)
        
        time = np.arange(len(imf)) / self.sample_rate
        ax.plot(time, imf, color=BUTTON_COLOR, linewidth=2.0)
        ax.set_title(f'本征模函数 (IMF) - 频带 {low_freq}-{high_freq} 赫兹', fontsize=22, fontweight='bold')
        ax.set_xlabel('时间 (秒)', fontsize=18)
        ax.set_ylabel('幅值', fontsize=18)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=16)

    def on_window_resize(self, event):
        """当窗口大小改变时调整显示"""
        # 仅处理主窗口大小变化，忽略子组件的大小变化事件
        if event.widget == self.root and hasattr(self, 'result_canvas'):
            # 如果有图形显示，则重新适应大小
            if hasattr(self, 'current_figs') and self.current_figs:
                # 延迟调用，等待布局稳定后再调整
                self.root.after(100, lambda: self.display_results_in_panel(self.current_figs))

def main():
    root = tk.Tk()
    app = TDMSAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main() 
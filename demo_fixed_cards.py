#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
展示修复卡片重叠问题后的故障异常报警系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import PRIMARY_BG, ACCENT_COLOR, TEXT_PRIMARY

class FixedCardsDemo(QMainWindow):
    """修复卡片重叠问题后的演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.show_alarm_system()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("修复卡片重叠问题 - 故障异常报警系统")
        self.setGeometry(50, 50, 1200, 900)
        self.setStyleSheet(f"background-color: {PRIMARY_BG};")
    
    def show_alarm_system(self):
        """直接显示故障异常报警系统"""
        self.alarm_system = FaultAlarmSystem()
        self.setCentralWidget(self.alarm_system)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("修复卡片重叠 - 故障异常报警系统")
    app.setApplicationVersion("1.2")
    
    # 创建演示窗口
    demo = FixedCardsDemo()
    demo.show()
    
    print("🎉 修复卡片重叠问题后的故障异常报警系统已启动")
    print("\n🔧 修复内容：")
    print("✅ 卡片尺寸调整：420×320 → 350×380")
    print("   - 宽度减少：420px → 350px (-70px)")
    print("   - 高度增加：320px → 380px (+60px)")
    print("✅ 卡片间距调整：20px → 15px")
    print("✅ 内边距优化：20px → 15px")
    print("✅ 内部间距调整：15px → 12px")
    print("\n📐 布局改进：")
    print("• 更窄的卡片避免水平重叠")
    print("• 更高的卡片提供足够的垂直空间")
    print("• 合理的间距确保卡片不相互遮挡")
    print("• 优化的内边距保持内容可读性")
    print("\n💡 设计原则：")
    print("• 内容优先：确保所有文字清晰可见")
    print("• 空间利用：在有限空间内最大化信息展示")
    print("• 视觉平衡：保持卡片之间的协调关系")
    print("• 响应式设计：适应不同屏幕尺寸")
    print("\n🎯 解决的问题：")
    print("• ❌ 卡片水平重叠 → ✅ 卡片完全分离")
    print("• ❌ 文字被遮挡 → ✅ 所有文字清晰可见")
    print("• ❌ 布局拥挤 → ✅ 布局舒适合理")
    print("• ❌ 内容显示不全 → ✅ 内容完整展示")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)

if __name__ == "__main__":
    main()

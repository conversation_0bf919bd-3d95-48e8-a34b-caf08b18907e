#!/usr/bin/env python3
"""
测试EMD子图显示效果
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_emd_subplots():
    """测试EMD子图显示"""
    print("测试EMD子图显示...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from algorithms import get_algorithm_interface
        
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建算法接口实例
        sample_rate = 1000
        interface = get_algorithm_interface(sample_rate)
        
        # 生成测试信号（复合信号）
        t = np.linspace(0, 2, sample_rate * 2)
        
        # 创建包含多个频率分量的信号
        signal1 = np.sin(2 * np.pi * 5 * t)      # 5Hz
        signal2 = 0.5 * np.sin(2 * np.pi * 20 * t)  # 20Hz
        signal3 = 0.3 * np.sin(2 * np.pi * 50 * t)  # 50Hz
        noise = 0.1 * np.random.randn(len(t))
        
        test_signal = signal1 + signal2 + signal3 + noise
        
        print(f"✓ 生成测试信号，长度: {len(test_signal)}")
        
        # 创建图形和轴
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 测试新的EMD子图显示
        interface.analyze_signal(test_signal, 12, ax)  # EMD是第12个方法
        
        # 保存测试图片
        output_dir = "analysis_results"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        output_path = os.path.join(output_dir, "emd_subplots_test.png")
        fig.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)
        
        print(f"✓ EMD子图测试图片已保存: {output_path}")
        
        # 测试单独的EMD分解
        imfs = analyzer._perform_emd(test_signal)
        print(f"✓ EMD分解完成，提取了 {len(imfs)} 个IMF分量")
        
        # 验证IMF分量的特性
        for i, imf in enumerate(imfs):
            energy = np.var(imf)
            mean_freq = analyzer._estimate_mean_frequency(imf)
            print(f"  IMF{i+1}: 能量 = {energy:.6f}, 平均频率 ≈ {mean_freq:.2f} Hz")
        
        print("✓ EMD子图显示测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("=" * 60)
    print("EMD子图显示测试")
    print("=" * 60)
    
    test_emd_subplots()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\nEMD子图显示特性:")
    print("1. ✓ 原始信号单独显示在顶部")
    print("2. ✓ 每个IMF分量独立子图")
    print("3. ✓ 不同颜色区分各分量")
    print("4. ✓ 统一时间轴对齐")
    print("5. ✓ 清晰的标题和标签")
    print("6. ✓ 紧凑的布局设计")

if __name__ == "__main__":
    main()

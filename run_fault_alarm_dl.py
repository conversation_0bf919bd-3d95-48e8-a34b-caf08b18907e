#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
在DL环境中运行故障异常报警系统
"""

import sys
import os
import subprocess

def check_environment():
    """检查当前环境"""
    try:
        # 检查是否在DL环境中
        result = subprocess.run(['conda', 'info', '--envs'], 
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            envs = result.stdout
            if 'DL' in envs and '*' in envs:
                current_env = [line for line in envs.split('\n') if '*' in line]
                if current_env and 'DL' in current_env[0]:
                    print("✅ 当前运行在DL环境中")
                    return True
                else:
                    print("❌ 当前不在DL环境中")
                    return False
            else:
                print("❌ 未找到DL环境或当前不在DL环境中")
                return False
        else:
            print("❌ 无法检查conda环境")
            return False
    except Exception as e:
        print(f"❌ 检查环境时出错: {e}")
        return False

def run_in_dl_environment():
    """在DL环境中运行故障异常报警系统"""
    if not check_environment():
        print("\n正在切换到DL环境...")
        try:
            # 在DL环境中运行
            cmd = 'conda activate DL && python test_fault_alarm.py'
            subprocess.run(cmd, shell=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        except Exception as e:
            print(f"❌ 在DL环境中运行失败: {e}")
            print("\n请手动执行以下命令：")
            print("conda activate DL")
            print("python test_fault_alarm.py")
    else:
        # 直接运行
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        from ui.fault_alarm_system import FaultAlarmSystem
        
        app = QApplication(sys.argv)
        
        # 创建故障异常报警系统窗口
        window = FaultAlarmSystem()
        window.show()
        
        print("🚀 故障异常报警系统已启动")
        print("\n📋 功能说明：")
        print("1. 🔴 状态栏：显示当前系统状态（正常/警告/报警）")
        print("2. 📊 监测卡片：显示三种算法的检测结果")
        print("   - 故障判断：基于传统信号处理的故障检测")
        print("   - 传统分类器：SVM、随机森林等机器学习算法")
        print("   - 深度学习：ResNet-1D、CNN等深度学习模型")
        print("3. 📋 历史记录：点击按钮查看历史故障记录")
        print("4. ⏱️ 实时更新：系统每5秒自动更新状态")
        print("5. 🎨 配色方案：采用原系统的浅蓝色配色")
        print("\n💡 设计特点：")
        print("- 参考了HTML模板的排版设计")
        print("- 使用原系统的配色方案保持一致性")
        print("- 历史故障记录独立为子页面")
        print("- 响应式卡片布局，支持悬浮效果")
        print("- 实时状态更新和数据模拟")
        
        try:
            sys.exit(app.exec_())
        except KeyboardInterrupt:
            print("\n程序已退出")
            sys.exit(0)

if __name__ == "__main__":
    run_in_dl_environment()

"""
特征方法选择子页面
用于在独立窗口中选择特征提取方法
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QWidget, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QCursor

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, HIGHLIGHT_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, WARNING_COLOR
)


class FeatureMethodCard(QFrame):
    """特征方法选择卡片组件"""
    clicked = pyqtSignal(int)  # 发送卡片索引

    def __init__(self, method_info, index):
        super().__init__()
        self.method_info = method_info
        self.index = index
        self.selected = False
        self.init_ui()

    def init_ui(self):
        """初始化卡片界面"""
        self.setFixedSize(120, 120)  # 增大卡片尺寸
        self.setCursor(QCursor(Qt.PointingHandCursor))

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)

        # 图标标签
        self.icon_label = QLabel(self.method_info["icon"])
        self.icon_label.setAlignment(Qt.AlignCenter)
        self.icon_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                color: #ffffff;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(self.icon_label)

        # 方法名称标签
        self.name_label = QLabel(self.method_info["short_name"])
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {TEXT_PRIMARY};
                background: transparent;
                border: none;
                font-weight: bold;
                line-height: 1.2;
            }}
        """)
        layout.addWidget(self.name_label)

        self.update_style()

    def update_style(self):
        """更新卡片样式"""
        if self.selected:
            # 选中状态样式
            self.setStyleSheet(f"""
                FeatureMethodCard {{
                    background-color: {ACCENT_COLOR};
                    border: 3px solid {HIGHLIGHT_COLOR};
                    border-radius: 12px;
                }}
            """)
            self.icon_label.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    color: #ffffff;
                    background: transparent;
                    border: none;
                }
            """)
            self.name_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #ffffff;
                    background: transparent;
                    border: none;
                    font-weight: bold;
                    line-height: 1.2;
                }
            """)
        else:
            # 未选中状态样式
            self.setStyleSheet(f"""
                FeatureMethodCard {{
                    background-color: {SECONDARY_BG};
                    border: 2px solid #cccccc;
                    border-radius: 12px;
                }}
                FeatureMethodCard:hover {{
                    background-color: #e6f3ff;
                    border-color: {ACCENT_COLOR};
                }}
            """)
            self.icon_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 32px;
                    color: {ACCENT_COLOR};
                    background: transparent;
                    border: none;
                }}
            """)
            self.name_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 14px;
                    color: {TEXT_PRIMARY};
                    background: transparent;
                    border: none;
                    font-weight: bold;
                    line-height: 1.2;
                }}
            """)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.selected = not self.selected
            self.update_style()
            self.clicked.emit(self.index)

    def set_selected(self, selected):
        """设置选中状态"""
        self.selected = selected
        self.update_style()


class FeatureMethodSelector(QDialog):
    """特征方法选择对话框"""
    methods_selected = pyqtSignal(list)  # 发送选中的方法索引列表

    def __init__(self, parent=None, selected_methods=None):
        super().__init__(parent)
        self.selected_methods = selected_methods or []
        
        # 13种分析方法及其图标映射
        self.analysis_methods = [
            {"name": "时域有量纲特征值", "icon": "📊", "short_name": "有量纲\n特征"},
            {"name": "时域无量纲特征值", "icon": "📈", "short_name": "无量纲\n特征"},
            {"name": "自相关函数", "icon": "🔄", "short_name": "自相关\n函数"},
            {"name": "互相关函数", "icon": "↔️", "short_name": "互相关\n函数"},
            {"name": "频谱分析", "icon": "📊", "short_name": "频谱\n(FFT)"},
            {"name": "倒频谱", "icon": "🔄", "short_name": "倒频谱"},
            {"name": "包络谱", "icon": "📦", "short_name": "包络谱"},
            {"name": "阶比谱", "icon": "📏", "short_name": "阶比谱"},
            {"name": "功率谱", "icon": "⚡", "short_name": "功率谱"},
            {"name": "短时傅里叶变换", "icon": "🔍", "short_name": "STFT"},
            {"name": "魏格纳威尔分布", "icon": "🌊", "short_name": "WVD"},
            {"name": "小波变换", "icon": "〰️", "short_name": "小波变换"},
            {"name": "本征模函数", "icon": "🌀", "short_name": "EMD"}
        ]
        
        self.method_cards = []
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("选择特征提取方法")
        self.setFixedSize(800, 600)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 标题
        title_label = QLabel("请选择要使用的特征提取方法")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
                font-family: 'Microsoft YaHei';
            }}
        """)
        layout.addWidget(title_label)

        # 说明文字
        desc_label = QLabel("点击卡片进行选择，可以选择多个方法进行组合分析")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                color: {TEXT_SECONDARY};
                margin-bottom: 15px;
                font-family: 'Microsoft YaHei';
            }}
        """)
        layout.addWidget(desc_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {PRIMARY_BG};
                border: 2px solid {SECONDARY_BG};
                border-radius: 8px;
            }}
        """)

        scroll_widget = QWidget()
        scroll_widget.setStyleSheet(f"background-color: {PRIMARY_BG};")

        # 使用网格布局排列卡片
        grid_layout = QGridLayout(scroll_widget)
        grid_layout.setContentsMargins(20, 20, 20, 20)
        grid_layout.setSpacing(20)

        # 创建特征方法卡片
        cols = 5  # 每行5个卡片
        for i, method_info in enumerate(self.analysis_methods):
            card = FeatureMethodCard(method_info, i)
            card.clicked.connect(self.on_method_card_clicked)
            
            # 设置初始选中状态
            if i in self.selected_methods:
                card.set_selected(True)
            
            self.method_cards.append(card)

            row = i // cols
            col = i % cols
            grid_layout.addWidget(card, row, col)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        # 底部按钮区域
        self.create_button_area(layout)

    def create_button_area(self, parent_layout):
        """创建底部按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(15, 15, 15, 15)
        button_layout.setSpacing(15)

        # 全选按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all_methods)
        select_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-height: 40px;
                padding: 10px 20px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #7d6ef0;
            }}
        """)

        # 清除按钮
        clear_btn = QPushButton("清除")
        clear_btn.clicked.connect(self.clear_selection)
        clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {WARNING_COLOR};
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-height: 40px;
                padding: 10px 20px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #fdd85e;
            }}
        """)

        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(clear_btn)
        button_layout.addStretch()

        # 确认按钮
        confirm_btn = QPushButton("确认选择")
        confirm_btn.clicked.connect(self.confirm_selection)
        confirm_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-height: 40px;
                padding: 10px 30px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #00c9a7;
            }}
        """)

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #666666;
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-height: 40px;
                padding: 10px 30px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: #777777;
            }}
        """)

        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)

        parent_layout.addWidget(button_frame)

    def on_method_card_clicked(self, index):
        """方法卡片点击事件"""
        # 更新选中状态列表
        if index in self.selected_methods:
            self.selected_methods.remove(index)
        else:
            self.selected_methods.append(index)

    def select_all_methods(self):
        """全选所有方法"""
        self.selected_methods = list(range(len(self.analysis_methods)))
        for i, card in enumerate(self.method_cards):
            card.set_selected(True)

    def clear_selection(self):
        """清除所有选择"""
        self.selected_methods = []
        for card in self.method_cards:
            card.set_selected(False)

    def confirm_selection(self):
        """确认选择"""
        if not self.selected_methods:
            QMessageBox.warning(self, "提示", "请至少选择一种特征提取方法！")
            return
        
        self.methods_selected.emit(self.selected_methods)
        self.accept()

    def get_selected_methods(self):
        """获取选中的方法"""
        return self.selected_methods

# TDMS文件时频分析器

这是一个用于分析TDMS文件中传感器通道数据的Python程序，提供了13种不同的时频分析方法。

## 功能特点

### 时域特征分析
1. **时域有量纲特征值**：均值、最大值、最小值、峰值、峰峰值、方差、标准差、RMS
2. **时域无量纲特征值**：偏度、峰度、波形因子、峰值因子、脉冲因子、裕度因子、方差因子
3. **自相关函数**：分析信号的周期性特征
4. **互相关函数**：分析两个信号之间的相关性

### 频域特征分析
5. **频谱分析**：快速傅里叶变换(FFT)频谱
6. **倒频谱**：用于检测周期性故障
7. **包络谱**：用于轴承故障诊断
8. **阶比谱**：用于旋转机械分析
9. **功率谱**：功率谱密度分析

### 时频域特征分析
10. **短时傅里叶变换(STFT)**：时频分析
11. **魏格纳威尔分布(WVD)**：高分辨率时频分析
12. **小波变换**：多分辨率时频分析
13. **本征模函数(IMF)**：经验模态分解

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install numpy matplotlib nptdms scipy PyWavelets
```

## 使用方法

1. **运行程序**：
   ```bash
   python tdms_analyzer.py
   ```

2. **加载TDMS文件**：
   - 点击"选择TDMS文件"按钮
   - 选择要分析的TDMS文件
   - 程序会自动识别文件中的通道

3. **选择通道**：
   - 从下拉菜单中选择要分析的通道
   - 设置正确的采样率（Hz）

4. **选择分析方法**：
   - 从左侧列表中选择要使用的分析方法
   - 可以多选或使用"全选"按钮
   - 使用"清除选择"按钮取消选择

5. **开始分析**：
   - 点击"开始分析"按钮
   - 程序会在右侧显示分析结果图表

## 界面说明

- **文件选择区域**：显示当前选择的TDMS文件
- **通道选择区域**：选择要分析的通道和设置采样率
- **分析方法区域**：选择要使用的时频分析方法
- **结果显示区域**：显示分析结果的图表

## 分析结果说明

### 时域特征值
- **有量纲特征**：具有物理单位的特征值，如电压、加速度等
- **无量纲特征**：无单位的特征值，用于信号形态分析

### 频域分析
- **频谱**：显示信号在不同频率下的幅值分布
- **倒频谱**：用于检测周期性故障，如齿轮故障
- **包络谱**：用于轴承故障诊断
- **阶比谱**：用于旋转机械的阶次分析
- **功率谱**：显示信号在不同频率下的功率分布

### 时频分析
- **STFT**：提供时间和频率的联合分析
- **WVD**：提供高分辨率的时频分析
- **小波变换**：多分辨率分析，适合非平稳信号
- **IMF**：将信号分解为不同频率成分

## 注意事项

1. **采样率设置**：请确保设置正确的采样率，这对频域分析结果很重要
2. **数据长度**：对于某些分析方法，数据长度会影响计算精度
3. **内存使用**：分析大量数据时可能需要较多内存
4. **计算时间**：某些复杂分析方法（如小波变换、WVD）可能需要较长时间

## 故障排除

1. **文件加载失败**：检查TDMS文件是否损坏或格式不正确
2. **通道识别失败**：确保TDMS文件包含有效的通道数据
3. **分析错误**：检查采样率设置是否正确
4. **内存不足**：尝试分析较小的数据段

## 技术说明

- 使用`nptdms`库读取TDMS文件
- 使用`scipy`进行信号处理
- 使用`matplotlib`进行数据可视化
- 使用`PyWavelets`进行小波变换
- 使用`tkinter`构建图形用户界面

## 扩展功能

程序可以进一步扩展以下功能：
- 数据预处理（滤波、去噪）
- 更多时频分析方法
- 结果导出功能
- 批量处理功能
- 自定义分析参数 
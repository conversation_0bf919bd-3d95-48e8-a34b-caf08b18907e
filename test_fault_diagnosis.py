"""
故障判断功能测试脚本
"""

import sys
import numpy as np
from PyQt5.QtWidgets import QApplication
from ui.fault_diagnosis import FaultDiagnosis

def test_fault_diagnosis():
    """测试故障判断功能"""
    app = QApplication(sys.argv)
    
    # 创建故障判断页面
    fault_diagnosis = FaultDiagnosis()
    
    # 模拟特征数据
    test_features = {
        '均值': 0.05,
        '均方根': 0.8,
        '方差': 0.3,
        '标准差': 0.55,
        '偏度': 0.2,
        '峰度': 3.5,
        '最大值': 2.1,
        '最小值': -2.0,
        '峰峰值': 4.1,
        '脉冲因子': 4.2,
        '裕度因子': 8.5,
        '波形因子': 1.8,
        '峰值因子': 4.0,
        '方差因子': 0.6,
        '主频率': 85.0,
        '主频率幅值': 120.0,
        '频谱重心': 65.0,
        '频谱方差': 2500.0,
        '包络均值': 0.4,
        '包络标准差': 0.2,
        '包络峰值因子': 3.8
    }
    
    # 模拟文件信息
    test_file_info = {
        'TDMS文件名': 'test_bearing_data',
        '车型': '测试车型',
        '部件': '轴承',
        '传感器类型': '振动传感器',
        '传感器编号': 'VS001'
    }
    
    # 设置特征数据
    fault_diagnosis.set_feature_data(test_features, test_file_info, 'Channel_1')
    
    # 显示窗口
    fault_diagnosis.show()
    fault_diagnosis.resize(1400, 900)
    
    print("故障判断功能测试启动成功！")
    print(f"已加载 {len(test_features)} 个特征")
    print("测试说明：")
    print("1. 多特征马氏距离分类：选择3-6个特征，基于正负样本的期望值和协方差矩阵进行分类")
    print("2. 单特征马氏距离阈值：选择1个特征，基于正负样本的标准差计算分类阈值")
    print("请在界面中选择特征并进行故障诊断测试")
    
    return app.exec_()

if __name__ == "__main__":
    test_fault_diagnosis()

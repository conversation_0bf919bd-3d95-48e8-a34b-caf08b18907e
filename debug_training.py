"""
调试深度学习训练过程
"""

import sys
import os
import numpy as np
import pandas as pd

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from algorithms import create_deep_learning_classifier

def test_training():
    """测试训练过程"""
    print("开始测试深度学习训练...")
    
    try:
        # 创建分类器
        classifier = create_deep_learning_classifier()
        print(f"分类器创建成功，PyTorch可用: {classifier.is_available()}")
        
        if not classifier.is_available():
            print("PyTorch不可用，无法进行训练测试")
            return
        
        # 生成测试数据
        print("生成测试数据...")
        np.random.seed(42)
        n_samples = 1000
        n_features = 50
        
        X = np.random.randn(n_samples, n_features)
        y = np.random.choice(['正常', '内圈故障', '外圈故障', '滚动体故障'], n_samples)
        
        print(f"数据形状: X={X.shape}, y={y.shape}")
        print(f"标签分布: {np.unique(y, return_counts=True)}")
        
        # 准备数据
        print("准备训练数据...")
        X_train, X_test, y_train, y_test = classifier.prepare_data(X, y, test_size=0.2)
        print(f"训练数据: X_train={X_train.shape}, y_train={y_train.shape}")
        print(f"测试数据: X_test={X_test.shape}, y_test={y_test.shape}")
        
        # 配置参数
        config = {
            'architecture': '简单MLP',
            'hidden_units': [128, 64],
            'dropout_rate': 0.3,
            'epochs': 5,  # 减少训练轮数用于测试
            'batch_size': 32,
            'learning_rate': 0.001,
            'normalize': True,
            'shuffle': True
        }
        
        print("开始训练...")
        
        def progress_callback(progress, message):
            print(f"进度: {progress}% - {message}")
        
        def epoch_callback(epoch, train_loss, train_acc, val_loss, val_acc):
            print(f"Epoch {epoch}: Loss={train_loss:.4f}, Acc={train_acc:.4f}")
        
        # 训练模型
        training_result = classifier.train(
            X_train, y_train,
            architecture='简单MLP',
            config=config,
            progress_callback=progress_callback,
            epoch_callback=epoch_callback
        )
        
        print("训练完成!")
        print(f"训练结果: {training_result}")
        
        # 评估模型
        print("评估模型...")
        evaluation_result = classifier.evaluate(X_test, y_test)
        print(f"评估结果: {evaluation_result}")
        
    except Exception as e:
        print(f"训练过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_training()

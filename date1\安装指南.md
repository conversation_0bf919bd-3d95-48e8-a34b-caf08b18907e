# TDMS分析器安装指南

## 第一步：安装Python

### 方法1：从官网下载安装
1. 访问 https://www.python.org/downloads/
2. 下载Python 3.7或更高版本（推荐Python 3.9或3.10）
3. 运行安装程序
4. **重要**：安装时勾选"Add Python to PATH"选项
5. 完成安装

### 方法2：使用Microsoft Store（Windows 10/11）
1. 打开Microsoft Store
2. 搜索"Python"
3. 选择Python 3.9或3.10版本
4. 点击安装

### 验证安装
打开命令提示符或PowerShell，输入：
```bash
python --version
```
或
```bash
py --version
```

应该显示Python版本信息。

## 第二步：安装依赖包

### 方法1：使用requirements.txt（推荐）
1. 打开命令提示符或PowerShell
2. 导航到程序所在目录
3. 运行以下命令：
```bash
pip install -r requirements.txt
```

### 方法2：手动安装
```bash
pip install numpy
pip install matplotlib
pip install nptdms>=0.20.0
pip install scipy
pip install PyWavelets
```

### 验证安装
运行以下命令检查所有包是否正确安装：
```bash
python -c "import numpy, matplotlib, nptdms, scipy, pywt; print('所有依赖包安装成功！')"
```

## 第三步：测试TDMS文件读取

在运行主程序之前，建议先测试TDMS文件读取功能：

```bash
python test_tdms.py
```

这个测试脚本会：
- 检查nptdms库是否正确安装
- 测试TDMS文件读取功能
- 显示文件中的通道信息
- 验证数据读取是否正常

如果测试通过，就可以运行主程序了。

## 第四步：运行程序

### 方法1：使用批处理文件（推荐）
双击 `启动程序.bat` 文件

### 方法2：命令行运行
1. 打开命令提示符或PowerShell
2. 导航到程序所在目录
3. 运行：
```bash
python tdms_analyzer.py
```

## 常见问题解决

### 1. "python不是内部或外部命令"
**解决方案**：
- 重新安装Python，确保勾选"Add Python to PATH"
- 或者手动添加Python到系统PATH环境变量

### 2. "pip不是内部或外部命令"
**解决方案**：
- 重新安装Python
- 或者使用：`python -m pip install package_name`

### 3. "module nptdms has no attribute read"
**解决方案**：
- 这是nptdms库版本问题，请确保安装正确版本：
```bash
pip uninstall nptdms
pip install nptdms>=0.20.0
```

### 4. 依赖包安装失败
**解决方案**：
- 更新pip：`python -m pip install --upgrade pip`
- 使用国内镜像源：
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 5. 程序启动失败
**解决方案**：
- 检查Python版本是否为3.7或更高
- 确认所有依赖包已正确安装
- 检查TDMS文件是否存在且格式正确
- 运行测试脚本：`python test_tdms.py`

### 6. 图形界面显示异常
**解决方案**：
- 更新显卡驱动
- 尝试使用不同的matplotlib后端：
```python
import matplotlib
matplotlib.use('TkAgg')
```

## 系统要求

- **操作系统**：Windows 7/8/10/11
- **Python版本**：3.7或更高版本
- **内存**：建议4GB或更多
- **存储空间**：至少500MB可用空间

## 技术支持

如果遇到其他问题，请检查：
1. Python版本是否符合要求
2. 所有依赖包是否正确安装
3. TDMS文件是否有效
4. 系统是否有足够的权限
5. 运行测试脚本检查具体问题

## 更新说明

- 程序版本：1.0
- 支持的分析方法：13种
- 支持的文件格式：TDMS
- 界面语言：中文
- nptdms库版本要求：>=0.20.0 
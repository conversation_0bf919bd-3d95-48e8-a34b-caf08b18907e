#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征提取修复效果
验证extract_features_for_diagnosis方法是否能正确提取特征
"""

import sys
import os
import numpy as np
from nptdms import TdmsFile

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_feature_extraction_fix():
    """测试特征提取修复效果"""
    print("=" * 60)
    print("测试特征提取修复效果")
    print("=" * 60)
    
    try:
        # 导入算法接口
        from algorithms import get_algorithm_interface
        
        # 创建算法接口
        interface = get_algorithm_interface(sample_rate=1000)
        
        # 生成测试数据
        test_data = np.random.randn(2000) + 0.1 * np.sin(2 * np.pi * 50 * np.linspace(0, 2, 2000))
        
        # 测试extract_numerical_features方法
        features = interface.extract_numerical_features(test_data)
        
        print(f"✓ 特征提取成功")
        print(f"✓ 提取了 {len(features)} 个特征")
        print(f"✓ 特征列表: {list(features.keys())}")
        
        # 验证特征的合理性
        expected_features = ['均值', '标准差', '方差', '均方根', '最大值', '最小值', '峰峰值',
                           '偏度', '峰度', '峰值因子', '裕度因子', '脉冲因子', '波形因子',
                           '频谱重心', '频谱方差', '频谱滚降', '频谱通量']
        
        missing_features = []
        for expected in expected_features:
            if expected not in features:
                missing_features.append(expected)
        
        if missing_features:
            print(f"⚠️  缺少特征: {missing_features}")
        else:
            print("✓ 所有预期特征都已提取")
        
        # 检查特征值是否合理
        valid_features = 0
        for name, value in features.items():
            if isinstance(value, (int, float)) and not np.isnan(value) and not np.isinf(value):
                valid_features += 1
            else:
                print(f"⚠️  特征 {name} 的值不合理: {value}")
        
        print(f"✓ 有效特征数量: {valid_features}/{len(features)}")
        
        if len(features) > 0 and valid_features == len(features):
            print("\n🎉 特征提取修复成功！")
            return True
        else:
            print("\n❌ 特征提取仍有问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tdms_file_reading():
    """测试TDMS文件读取"""
    print("\n" + "=" * 60)
    print("测试TDMS文件读取")
    print("=" * 60)
    
    # 查找TDMS文件
    tdms_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.tdms'):
                tdms_files.append(os.path.join(root, file))
    
    if not tdms_files:
        print("⚠️  未找到TDMS文件，跳过文件读取测试")
        return True
    
    # 测试第一个TDMS文件
    test_file = tdms_files[0]
    print(f"测试文件: {test_file}")
    
    try:
        with TdmsFile.read(test_file) as tdms_file:
            print(f"✓ 文件读取成功")
            
            # 获取通道信息
            channels = []
            for group in tdms_file.groups():
                for channel in group.channels():
                    channel_name = f"{group.name}/{channel.name}"
                    channels.append(channel_name)
            
            print(f"✓ 找到 {len(channels)} 个通道")
            
            if channels:
                # 测试读取第一个通道的数据
                first_channel = channels[0]
                print(f"测试通道: {first_channel}")
                
                group_name, channel_name = first_channel.split('/', 1)
                channel = tdms_file[group_name][channel_name]
                data = channel[:]
                
                print(f"✓ 通道数据读取成功，数据长度: {len(data)}")
                
                if len(data) > 0:
                    # 测试特征提取
                    from algorithms import get_algorithm_interface
                    interface = get_algorithm_interface(sample_rate=1000)
                    features = interface.extract_numerical_features(data)
                    
                    print(f"✓ 从TDMS数据提取了 {len(features)} 个特征")
                    return True
                else:
                    print("⚠️  通道数据为空")
                    return False
            else:
                print("⚠️  未找到通道")
                return False
                
    except Exception as e:
        print(f"❌ TDMS文件读取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("特征提取修复验证测试")
    print("=" * 60)
    
    # 测试1: 基本特征提取
    test1_result = test_feature_extraction_fix()
    
    # 测试2: TDMS文件读取和特征提取
    test2_result = test_tdms_file_reading()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！特征提取修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

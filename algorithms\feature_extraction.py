"""
特征提取算法模块
包含13种特征提取与分析方法的具体实现
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq, ifft
from scipy.stats import kurtosis, skew
import pywt
from scipy.signal import hilbert
from matplotlib.colors import LinearSegmentedColormap
from .emd_utils import EMDProcessor


class FeatureExtractor:
    """特征提取器类"""

    def __init__(self, sample_rate=1000):
        """
        初始化特征提取器
        
        Args:
            sample_rate (float): 采样率，默认1000Hz
        """
        self.sample_rate = sample_rate
        self.extracted_features = {}  # 存储提取的数值特征
        self.emd_processor = EMDProcessor(sample_rate)  # EMD处理器
        
        # 13种分析方法名称
        self.method_names = [
            "时域有量纲特征值",
            "时域无量纲特征值", 
            "自相关函数",
            "互相关函数",
            "频谱分析",
            "倒频谱",
            "包络谱",
            "阶比谱",
            "功率谱",
            "短时傅里叶变换",
            "魏格纳威尔分布",
            "小波变换",
            "本征模函数"
        ]

    def extract_numerical_features(self, data):
        """
        提取数值特征用于故障判断
        
        Args:
            data (array): 信号数据
            
        Returns:
            dict: 提取的数值特征
        """
        features = {}
        
        # 时域有量纲特征
        features['均值'] = np.mean(data)
        features['标准差'] = np.std(data)
        features['方差'] = np.var(data)
        features['均方根'] = np.sqrt(np.mean(data**2))
        features['最大值'] = np.max(data)
        features['最小值'] = np.min(data)
        features['峰峰值'] = np.max(data) - np.min(data)
        
        # 时域无量纲特征
        features['偏度'] = skew(data)
        features['峰度'] = kurtosis(data)
        features['峰值因子'] = np.max(np.abs(data)) / np.sqrt(np.mean(data**2))
        features['裕度因子'] = np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2
        features['脉冲因子'] = np.max(np.abs(data)) / np.mean(np.abs(data))
        features['波形因子'] = np.sqrt(np.mean(data**2)) / np.mean(np.abs(data))
        
        # 频域特征
        fft_data = fft(data)
        freqs = fftfreq(len(data), 1/self.sample_rate)
        magnitude = np.abs(fft_data)
        
        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_magnitude = magnitude[:len(magnitude)//2]
        
        features['频谱重心'] = np.sum(positive_freqs * positive_magnitude) / np.sum(positive_magnitude)
        features['频谱方差'] = np.sqrt(np.sum(((positive_freqs - features['频谱重心'])**2) * positive_magnitude) / np.sum(positive_magnitude))
        features['频谱滚降'] = self._calculate_spectral_rolloff(positive_freqs, positive_magnitude)
        features['频谱通量'] = np.sum(np.diff(positive_magnitude)**2)
        
        self.extracted_features = features
        return features

    def _calculate_spectral_rolloff(self, freqs, magnitude, rolloff_percent=0.85):
        """计算频谱滚降点"""
        total_energy = np.sum(magnitude)
        cumulative_energy = np.cumsum(magnitude)
        rolloff_index = np.where(cumulative_energy >= rolloff_percent * total_energy)[0]
        if len(rolloff_index) > 0:
            return freqs[rolloff_index[0]]
        return freqs[-1]

    def extract_features(self, data, methods=None):
        """
        提取特征
        
        Args:
            data (array): 信号数据
            methods (list): 方法索引列表，None表示提取所有特征
            
        Returns:
            dict: 特征提取结果
        """
        if methods is None:
            methods = list(range(len(self.method_names)))
        
        results = {}
        
        for method_idx in methods:
            if 0 <= method_idx < len(self.method_names):
                method_name = self.method_names[method_idx]
                try:
                    if method_idx in [0, 1]:  # 时域特征
                        features = self._extract_time_domain_features(data, method_idx)
                        results[method_name] = features
                    else:
                        # 其他方法返回处理后的数据
                        processed_data = self._process_signal_method(data, method_idx)
                        results[method_name] = processed_data
                except Exception as e:
                    results[method_name] = {'error': str(e)}
        
        return results

    def analyze_signal(self, data, method_index, ax=None):
        """
        分析信号并可选择性地绘图
        
        Args:
            data (array): 信号数据
            method_index (int): 分析方法索引
            ax (matplotlib.axes): 绘图轴，可选
            
        Returns:
            dict: 分析结果
        """
        if not (0 <= method_index < len(self.method_names)):
            raise ValueError(f"方法索引超出范围: {method_index}")
        
        method_name = self.method_names[method_index]
        
        try:
            # 执行对应的分析方法
            if method_index == 0:
                return self.analyze_time_domain_dimensional(data, ax)
            elif method_index == 1:
                return self.analyze_time_domain_dimensionless(data, ax)
            elif method_index == 2:
                return self.analyze_autocorrelation(data, ax)
            elif method_index == 3:
                return self.analyze_crosscorrelation(data, ax)
            elif method_index == 4:
                return self.analyze_spectrum(data, ax)
            elif method_index == 5:
                return self.analyze_cepstrum(data, ax)
            elif method_index == 6:
                return self.analyze_envelope_spectrum(data, ax)
            elif method_index == 7:
                return self.analyze_order_spectrum(data, ax)
            elif method_index == 8:
                return self.analyze_power_spectrum(data, ax)
            elif method_index == 9:
                return self.analyze_stft(data, ax)
            elif method_index == 10:
                return self.analyze_wigner_ville(data, ax)
            elif method_index == 11:
                return self.analyze_wavelet(data, ax)
            elif method_index == 12:
                return self.analyze_emd(data, ax)
            else:
                raise ValueError(f"未实现的方法索引: {method_index}")
                
        except Exception as e:
            return {'error': str(e), 'method': method_name}

    def _extract_time_domain_features(self, data, method_type):
        """提取时域特征"""
        if method_type == 0:  # 有量纲特征
            return {
                '均值': np.mean(data),
                '标准差': np.std(data),
                '方差': np.var(data),
                '均方根': np.sqrt(np.mean(data**2)),
                '最大值': np.max(data),
                '最小值': np.min(data),
                '峰峰值': np.max(data) - np.min(data)
            }
        else:  # 无量纲特征
            return {
                '偏度': skew(data),
                '峰度': kurtosis(data),
                '峰值因子': np.max(np.abs(data)) / np.sqrt(np.mean(data**2)),
                '裕度因子': np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2,
                '脉冲因子': np.max(np.abs(data)) / np.mean(np.abs(data)),
                '波形因子': np.sqrt(np.mean(data**2)) / np.mean(np.abs(data))
            }

    def _process_signal_method(self, data, method_index):
        """处理信号方法，返回处理后的数据"""
        # 这里返回基本的处理结果，具体的绘图在analyze_signal中处理
        if method_index == 4:  # 频谱分析
            fft_data = fft(data)
            freqs = fftfreq(len(data), 1/self.sample_rate)
            return {'frequencies': freqs[:len(freqs)//2], 'magnitude': np.abs(fft_data)[:len(fft_data)//2]}
        # 其他方法的基本处理...
        return {'processed_data': data}

    # 以下是具体的分析方法实现，从ui/analysis_methods.py迁移过来
    def analyze_time_domain_dimensional(self, data, ax):
        """时域有量纲特征分析"""
        try:
            features = {
                '均值': np.mean(data),
                '均方根': np.sqrt(np.mean(data**2)),
                '方差': np.var(data),
                '标准差': np.std(data),
                '偏度': skew(data),
                '峰度': kurtosis(data),
                '最大值': np.max(data),
                '最小值': np.min(data),
                '峰峰值': np.max(data) - np.min(data),
                '脉冲因子': np.max(np.abs(data)) / np.mean(np.abs(data)),
                '裕度因子': np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2,
                '波形因子': np.sqrt(np.mean(data**2)) / np.mean(np.abs(data)),
                '峰值因子': np.max(np.abs(data)) / np.sqrt(np.mean(data**2)),
            }

            if ax is not None:
                # 绘制特征值
                names = list(features.keys())
                values = list(features.values())

                bars = ax.bar(names, values, color='#3498DB', alpha=0.8)
                ax.set_ylabel('数值', fontsize=12)

                # 调整x轴标签
                ax.set_xticks(range(len(names)))
                ax.set_xticklabels(names, rotation=75, ha='right', fontsize=8)
                ax.tick_params(axis='y', labelsize=10)
                ax.set_xlabel('特征类型', fontsize=12, labelpad=25)

                # 在柱状图上添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.3f}', ha='center', va='bottom', fontsize=8)

                ax.grid(True, alpha=0.3)

            return {'features': features, 'type': 'time_domain_dimensional'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'时域有量纲分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'time_domain_dimensional'}

    def analyze_time_domain_dimensionless(self, data, ax):
        """时域无量纲特征分析"""
        try:
            features = {
                '偏度': skew(data),
                '峰度': kurtosis(data),
                '波形因子': np.sqrt(np.mean(data**2)) / np.mean(np.abs(data)),
                '峰值因子': np.max(np.abs(data)) / np.sqrt(np.mean(data**2)),
                '脉冲因子': np.max(np.abs(data)) / np.mean(np.abs(data)),
                '裕度因子': np.max(np.abs(data)) / (np.mean(np.sqrt(np.abs(data))))**2,
                '方差因子': np.var(data) / np.mean(data)**2 if np.mean(data) != 0 else 0
            }

            if ax is not None:
                # 绘制特征值
                names = list(features.keys())
                values = list(features.values())

                bars = ax.bar(names, values, color='#E74C3C', alpha=0.8)
                ax.set_ylabel('数值', fontsize=12)

                # 调整x轴标签
                ax.set_xticks(range(len(names)))
                ax.set_xticklabels(names, rotation=75, ha='right', fontsize=8)
                ax.tick_params(axis='y', labelsize=10)
                ax.set_xlabel('特征类型', fontsize=12, labelpad=25)

                # 在柱状图上添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.3f}', ha='center', va='bottom', fontsize=8)

                ax.grid(True, alpha=0.3)

            return {'features': features, 'type': 'time_domain_dimensionless'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'时域无量纲分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'time_domain_dimensionless'}

    def analyze_autocorrelation(self, data, ax):
        """自相关函数分析"""
        try:
            # 计算自相关函数
            autocorr = signal.correlate(data, data, mode='full')
            autocorr = autocorr[len(data)-1:]

            # 归一化
            autocorr = autocorr / autocorr[0]

            if ax is not None:
                time = np.arange(len(autocorr)) / self.sample_rate
                ax.plot(time, autocorr, color='#3498DB', linewidth=2.0)
                ax.set_xlabel('时间 (秒)', fontsize=14)
                ax.set_ylabel('自相关系数', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'autocorr': autocorr, 'type': 'autocorrelation'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'自相关分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'autocorrelation'}

    def analyze_crosscorrelation(self, data, ax):
        """互相关函数分析"""
        try:
            # 创建延迟版本
            delay = len(data) // 4
            delayed_data = np.roll(data, delay)

            # 计算互相关函数
            crosscorr = signal.correlate(data, delayed_data, mode='full')
            crosscorr = crosscorr[len(data)-1:]

            # 归一化
            crosscorr = crosscorr / np.sqrt(np.sum(data**2) * np.sum(delayed_data**2))

            if ax is not None:
                time = np.arange(len(crosscorr)) / self.sample_rate
                ax.plot(time, crosscorr, color='#3498DB', linewidth=2.0)
                ax.set_xlabel('时间 (秒)', fontsize=14)
                ax.set_ylabel('互相关系数', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'crosscorr': crosscorr, 'type': 'crosscorrelation'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'互相关分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'crosscorrelation'}

    def analyze_spectrum(self, data, ax):
        """频谱分析"""
        try:
            # 计算FFT
            fft_data = fft(data)
            freqs = fftfreq(len(data), 1/self.sample_rate)

            # 只显示正频率部分
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = np.abs(fft_data[:len(freqs)//2])

            if ax is not None:
                ax.plot(positive_freqs, positive_fft, color='#3498DB', linewidth=2.0)
                ax.set_xlabel('频率 (Hz)', fontsize=14)
                ax.set_ylabel('幅值', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'frequencies': positive_freqs, 'magnitude': positive_fft, 'type': 'spectrum'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'频谱分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'spectrum'}

    def analyze_cepstrum(self, data, ax):
        """倒频谱分析"""
        try:
            # 计算倒频谱
            fft_data = fft(data)
            log_spectrum = np.log(np.abs(fft_data) + 1e-10)
            cepstrum = np.abs(ifft(log_spectrum))

            if ax is not None:
                time = np.arange(len(cepstrum)) / self.sample_rate
                ax.plot(time[:len(time)//2], cepstrum[:len(cepstrum)//2], color='#3498DB', linewidth=2.0)
                ax.set_xlabel('倒频率 (秒)', fontsize=14)
                ax.set_ylabel('幅值', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'cepstrum': cepstrum, 'type': 'cepstrum'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'倒频谱分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'cepstrum'}

    def analyze_envelope_spectrum(self, data, ax):
        """包络谱分析"""
        try:
            # 计算解析信号
            analytic_signal = hilbert(data)
            envelope = np.abs(analytic_signal)

            # 计算包络的频谱
            fft_envelope = fft(envelope)
            freqs = fftfreq(len(envelope), 1/self.sample_rate)

            # 只显示正频率部分
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = np.abs(fft_envelope[:len(freqs)//2])

            if ax is not None:
                ax.plot(positive_freqs, positive_fft, color='#3498DB', linewidth=2.0)
                ax.set_xlabel('频率 (Hz)', fontsize=14)
                ax.set_ylabel('幅值', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'frequencies': positive_freqs, 'magnitude': positive_fft, 'type': 'envelope_spectrum'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'包络谱分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'envelope_spectrum'}

    def analyze_order_spectrum(self, data, ax):
        """阶比谱分析"""
        try:
            # 假设转速为60Hz（3600rpm）
            rpm = 3600
            order_freq = rpm / 60  # Hz

            # 计算阶比
            freqs = fftfreq(len(data), 1/self.sample_rate)
            orders = freqs / order_freq

            fft_data = fft(data)
            fft_magnitude = np.abs(fft_data)

            # 只显示正阶比部分
            positive_orders = orders[:len(orders)//2]
            positive_fft = fft_magnitude[:len(orders)//2]

            if ax is not None:
                ax.plot(positive_orders, positive_fft, color='#3498DB', linewidth=2.0)
                ax.set_xlabel('阶比', fontsize=14)
                ax.set_ylabel('幅值', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'orders': positive_orders, 'magnitude': positive_fft, 'type': 'order_spectrum'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'阶比谱分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'order_spectrum'}

    def analyze_power_spectrum(self, data, ax):
        """功率谱分析"""
        try:
            freqs, psd = signal.welch(data, self.sample_rate, nperseg=1024)

            if ax is not None:
                ax.semilogy(freqs, psd, color='#3498DB', linewidth=2.0)
                ax.set_xlabel('频率 (Hz)', fontsize=14)
                ax.set_ylabel('功率谱密度', fontsize=14)
                ax.tick_params(labelsize=12)
                ax.grid(True, alpha=0.3)

            return {'frequencies': freqs, 'psd': psd, 'type': 'power_spectrum'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'功率谱分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'power_spectrum'}

    def analyze_stft(self, data, ax):
        """短时傅里叶变换"""
        try:
            # 计算STFT
            f, t, Zxx = signal.stft(data, self.sample_rate, nperseg=256, noverlap=128)

            if ax is not None:
                # 绘制时频图
                im = ax.pcolormesh(t, f, np.abs(Zxx), shading='gouraud', cmap='viridis')
                ax.set_xlabel('时间 (秒)', fontsize=14)
                ax.set_ylabel('频率 (Hz)', fontsize=14)
                ax.tick_params(labelsize=12)

                # 添加颜色条
                cbar = plt.colorbar(im, ax=ax)
                cbar.set_label('幅值', fontsize=12)
                cbar.ax.tick_params(labelsize=10)

            return {'time': t, 'frequency': f, 'magnitude': np.abs(Zxx), 'type': 'stft'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'STFT分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'stft'}

    def analyze_wigner_ville(self, data, ax):
        """魏格纳威尔分布"""
        try:
            # 使用STFT的平方作为近似
            f, t, Zxx = signal.stft(data, self.sample_rate, nperseg=256, noverlap=128)

            # 计算WVD近似
            wvd = np.abs(Zxx)**2

            if ax is not None:
                im = ax.pcolormesh(t, f, wvd, shading='gouraud', cmap='plasma')
                ax.set_xlabel('时间 (秒)', fontsize=14)
                ax.set_ylabel('频率 (Hz)', fontsize=14)
                ax.tick_params(labelsize=12)

                # 添加颜色条
                cbar = plt.colorbar(im, ax=ax)
                cbar.set_label('功率', fontsize=12)
                cbar.ax.tick_params(labelsize=10)

            return {'time': t, 'frequency': f, 'wvd': wvd, 'type': 'wigner_ville'}

        except Exception as e:
            if ax is not None:
                ax.text(0.5, 0.5, f'WVD分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
            return {'error': str(e), 'type': 'wigner_ville'}

    def analyze_wavelet(self, data, ax):
        """小波变换"""
        try:
            # 使用快速模式
            max_length = 5000
            num_scales = 16

            if len(data) > max_length:
                data_subset = data[:max_length]
            else:
                data_subset = data

            # 使用对数分布的尺度
            scales = np.logspace(0, 2, num_scales)
            wavelet = 'cmor1.5-1.0'

            # 计算小波变换
            coefficients, frequencies = pywt.cwt(data_subset, scales, wavelet, 1/self.sample_rate)

            if ax is not None:
                # 绘制小波变换结果
                time = np.arange(len(data_subset)) / self.sample_rate
                im = ax.pcolormesh(time, frequencies, np.abs(coefficients), shading='gouraud', cmap='jet')
                ax.set_xlabel('时间 (秒)', fontsize=14)
                ax.set_ylabel('频率 (Hz)', fontsize=14)
                ax.tick_params(labelsize=12)

                # 添加颜色条
                cbar = plt.colorbar(im, ax=ax)
                cbar.set_label('幅值', fontsize=12)
                cbar.ax.tick_params(labelsize=10)

            return {'time': np.arange(len(data_subset)) / self.sample_rate,
                   'frequencies': frequencies,
                   'coefficients': coefficients,
                   'type': 'wavelet'}

        except Exception as e:
            # 如果小波变换失败，使用STFT替代
            try:
                return self._analyze_stft_alternative(data, ax)
            except Exception as e2:
                if ax is not None:
                    ax.text(0.5, 0.5, f'小波变换失败:\n{str(e)}',
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=12, color='red')
                return {'error': str(e), 'type': 'wavelet'}

    def _analyze_stft_alternative(self, data, ax):
        """小波变换的STFT替代方法"""
        max_length = 10000
        if len(data) > max_length:
            data_subset = data[:max_length]
        else:
            data_subset = data

        f, t, Zxx = signal.stft(data_subset, self.sample_rate, nperseg=256, noverlap=128)
        power_spectrum = np.abs(Zxx)**2

        if ax is not None:
            im = ax.pcolormesh(t, f, power_spectrum, shading='gouraud', cmap='hot')
            ax.set_xlabel('时间 (秒)', fontsize=14)
            ax.set_ylabel('频率 (Hz)', fontsize=14)
            ax.tick_params(labelsize=12)

            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('功率', fontsize=12)
            cbar.ax.tick_params(labelsize=10)

        return {'time': t, 'frequency': f, 'power_spectrum': power_spectrum, 'type': 'stft_alternative'}

    def analyze_emd(self, data, ax):
        """经验模态分解(EMD) - 每个IMF显示在单独子图中"""
        try:
            # 使用EMD处理器
            imfs = self.emd_processor.perform_emd(data)

            if len(imfs) == 0:
                if ax is not None:
                    ax.text(0.5, 0.5, 'EMD分解失败\n未能提取有效的IMF分量',
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=14, color='red')
                return {'error': 'EMD分解失败', 'type': 'emd'}

            # 创建时间轴
            time_axis = np.arange(len(data)) / self.sample_rate

            if ax is not None:
                # 显示前几个IMF分量
                n_imfs = min(6, len(imfs))  # 最多显示6个IMF分量

                # 清除当前轴并创建子图
                ax.clear()

                # 获取父图形
                fig = ax.get_figure()

                # 删除原来的轴
                fig.delaxes(ax)

                # 计算子图布局 - 原始信号 + IMF分量
                n_plots = n_imfs + 1  # 原始信号 + IMF分量

                # 创建子图
                axes = []
                for i in range(n_plots):
                    subplot_ax = fig.add_subplot(n_plots, 1, i + 1)
                    axes.append(subplot_ax)

                # 绘制原始信号
                axes[0].plot(time_axis, data, color='#2C3E50', linewidth=1.5, alpha=0.8)
                axes[0].set_title('原始信号', fontsize=12, pad=5)
                axes[0].grid(True, alpha=0.3)
                axes[0].tick_params(labelsize=8)
                axes[0].set_ylabel('幅值', fontsize=10)

                # 使用不同颜色绘制各个IMF分量
                colors = ['#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6', '#1ABC9C']

                # 绘制IMF分量
                for i in range(n_imfs):
                    color = colors[i % len(colors)]
                    axes[i + 1].plot(time_axis, imfs[i],
                                   color=color, linewidth=1.2, alpha=0.8)
                    axes[i + 1].set_title(f'IMF{i+1}', fontsize=12, pad=5)
                    axes[i + 1].grid(True, alpha=0.3)
                    axes[i + 1].tick_params(labelsize=8)
                    axes[i + 1].set_ylabel('幅值', fontsize=10)

                    # 只在最后一个子图添加x轴标签
                    if i == n_imfs - 1:
                        axes[i + 1].set_xlabel('时间 (秒)', fontsize=10)

                # 调整子图间距
                fig.tight_layout(pad=1.0)

                # 添加总标题
                fig.suptitle(f'EMD分解结果 (共{len(imfs)}个分量)',
                            fontsize=14, y=0.98)

            return {'imfs': imfs, 'time': time_axis, 'type': 'emd'}

        except Exception as e:
            # 如果出错，在原轴上显示错误信息
            if ax is not None:
                ax.text(0.5, 0.5, f'EMD分析失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=14, color='red')
            return {'error': str(e), 'type': 'emd'}

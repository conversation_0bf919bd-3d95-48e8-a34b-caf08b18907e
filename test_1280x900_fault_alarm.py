#!/usr/bin/env python3
"""
测试适应1280x900分辨率的故障异常报警系统界面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QHBoxLayout, QLabel, QFrame, QSplitter)
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import get_main_stylesheet, PRIMARY_BG, TEXT_PRIMARY


class MockNavigationBar(QWidget):
    """模拟导航栏"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化导航栏"""
        self.setFixedWidth(280)  # 与主程序相同的导航栏宽度
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_BG};
                border-right: 2px solid #cccccc;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 20, 10, 20)
        
        # 模拟导航项目
        nav_items = [
            "🏠 仪表盘",
            "📁 文件选择", 
            "📊 特征提取与分析",
            "🔧 故障判断",
            "🔍 经典分类器监测",
            "🧠 深度学习监测",
            "⚠️ 故障异常报警",  # 当前页面
            "📋 检测报告生成",
            "⚙️ 系统设置"
        ]
        
        for i, item in enumerate(nav_items):
            label = QLabel(item)
            if i == 6:  # 当前页面高亮
                label.setStyleSheet(f"""
                    QLabel {{
                        background-color: #0066cc;
                        color: white;
                        padding: 12px;
                        border-radius: 8px;
                        font-weight: bold;
                        font-size: 14px;
                    }}
                """)
            else:
                label.setStyleSheet(f"""
                    QLabel {{
                        color: {TEXT_PRIMARY};
                        padding: 12px;
                        font-size: 14px;
                    }}
                """)
            layout.addWidget(label)
        
        layout.addStretch()


class Test1280x900Window(QMainWindow):
    """测试1280x900分辨率的窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("故障异常报警系统 - 1280x900分辨率测试")
        self.setGeometry(100, 100, 1280, 900)  # 设置为1280x900分辨率
        
        # 设置样式
        self.setStyleSheet(get_main_stylesheet())
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 顶部标题栏
        self.create_top_bar(main_layout)
        
        # 创建分割器模拟主程序布局
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #0066cc;
            }
        """)
        
        # 左侧导航栏（模拟）
        self.navigation = MockNavigationBar()
        splitter.addWidget(self.navigation)
        
        # 右侧工作区
        workspace_container = QWidget()
        workspace_layout = QVBoxLayout(workspace_container)
        workspace_layout.setContentsMargins(0, 0, 0, 0)
        workspace_layout.setSpacing(0)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        workspace_layout.addWidget(self.fault_alarm)
        
        splitter.addWidget(workspace_container)
        
        # 设置分割器比例（与主程序相同）
        splitter.setSizes([280, 1000])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # 底部状态栏
        self.create_status_bar(main_layout)
    
    def create_top_bar(self, parent_layout):
        """创建顶部标题栏"""
        top_bar = QFrame()
        top_bar.setFixedHeight(60)
        top_bar.setStyleSheet(f"""
            QFrame {{
                background-color: {PRIMARY_BG};
                border-bottom: 2px solid #cccccc;
            }}
        """)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # 左侧菜单模拟
        menu_label = QLabel("文件  工具  帮助")
        menu_label.setStyleSheet(f"""
            color: {TEXT_PRIMARY};
            font-size: 14px;
        """)
        top_layout.addWidget(menu_label)
        
        top_layout.addStretch()
        
        # 右侧公司标识
        company_label = QLabel("解放军七四三五工厂")
        company_label.setStyleSheet(f"""
            color: {TEXT_PRIMARY};
            font-size: 18px;
            font-weight: bold;
        """)
        top_layout.addWidget(company_label)
        
        parent_layout.addWidget(top_bar)
    
    def create_status_bar(self, parent_layout):
        """创建底部状态栏"""
        status_bar = QFrame()
        status_bar.setFixedHeight(30)
        status_bar.setStyleSheet(f"""
            QFrame {{
                background-color: {PRIMARY_BG};
                border-top: 1px solid #cccccc;
            }}
        """)
        
        status_layout = QHBoxLayout(status_bar)
        status_layout.setContentsMargins(10, 5, 10, 5)
        
        status_text = QLabel("数据库: 已连接  |  用户: admin  |  时间: 2025-08-04 17:43:10")
        status_text.setStyleSheet(f"""
            color: {TEXT_PRIMARY};
            font-size: 12px;
        """)
        status_layout.addWidget(status_text)
        
        parent_layout.addWidget(status_bar)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("故障异常报警系统 - 1280x900测试")
    app.setApplicationVersion("2.0")
    
    # 创建并显示测试窗口
    window = Test1280x900Window()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

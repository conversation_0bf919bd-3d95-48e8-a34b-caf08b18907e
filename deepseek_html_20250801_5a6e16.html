<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汽车轴承故障异常报警系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #1a2b3c;
            --secondary: #2c3e50;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --info: #3498db;
            --text-light: #ecf0f1;
            --text-dark: #2c3e50;
            --card-bg: rgba(255, 255, 255, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--text-light);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .status-bar.normal {
            border-color: var(--success);
        }
        
        .status-bar.warning {
            border-color: var(--warning);
        }
        
        .status-bar.alarm {
            border-color: var(--danger);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
            70% { box-shadow: 0 0 0 15px rgba(231, 76, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
        }
        
        .status-normal {
            background: var(--success);
        }
        
        .status-warning {
            background: var(--warning);
        }
        
        .status-alarm {
            background: var(--danger);
        }
        
        .status-text {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .last-update {
            text-align: right;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .monitoring-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .card-title {
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .status-normal-badge {
            background: var(--success);
            color: white;
        }
        
        .status-warning-badge {
            background: var(--warning);
            color: var(--text-dark);
        }
        
        .status-alarm-badge {
            background: var(--danger);
            color: white;
        }
        
        .card-content {
            margin-bottom: 20px;
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
        }
        
        .data-label {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .data-value {
            font-weight: bold;
        }
        
        .progress-container {
            margin: 20px 0;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .progress-bar {
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 5px;
        }
        
        .progress-normal {
            background: var(--success);
        }
        
        .progress-warning {
            background: var(--warning);
        }
        
        .progress-alarm {
            background: var(--danger);
        }
        
        .history-section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section-title {
            font-size: 1.6rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .history-table th {
            background: rgba(0, 0, 0, 0.2);
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .history-table td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .history-table tr:last-child td {
            border-bottom: none;
        }
        
        .history-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .fault-type {
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 0.85rem;
        }
        
        .fault-inner {
            background: rgba(241, 196, 15, 0.2);
            color: #f1c40f;
        }
        
        .fault-outer {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }
        
        .fault-cage {
            background: rgba(155, 89, 182, 0.2);
            color: #9b59b6;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 30px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--info);
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-light);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .btn-alarm {
            background: var(--danger);
            color: white;
        }
        
        .btn-alarm:hover {
            background: #c0392b;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .monitoring-cards {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 20px;
            }
            
            .last-update {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-car-crash"></i> 汽车轴承故障异常报警系统</h1>
            <p>基于多算法融合的实时监测与预警平台</p>
        </header>
        
        <div class="status-bar alarm" id="statusBar">
            <div class="status-indicator">
                <div class="status-icon status-alarm">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <div class="status-text">轴承故障报警</div>
                    <p>系统检测到异常情况，请立即检查！</p>
                </div>
            </div>
            <div class="last-update">
                <p>最后更新: <span id="lastUpdate">2023-10-15 14:28:45</span></p>
                <p>设备ID: BX-2037-08</p>
            </div>
        </div>
        
        <div class="monitoring-cards">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-search"></i> 故障判断</h2>
                    <span class="card-status status-alarm-badge">故障报警</span>
                </div>
                <div class="card-content">
                    <div class="data-item">
                        <span class="data-label">振动幅度</span>
                        <span class="data-value">8.7 mm/s</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">温度变化</span>
                        <span class="data-value">+12.5°C</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">噪声水平</span>
                        <span class="data-value">78 dB</span>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>故障概率</span>
                            <span>92%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-alarm" style="width: 92%"></div>
                        </div>
                    </div>
                    
                    <div class="data-item">
                        <span class="data-label">故障类型</span>
                        <span class="data-value">外圈损伤</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">置信度</span>
                        <span class="data-value">86.3%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-cogs"></i> 传统分类器监测</h2>
                    <span class="card-status status-normal-badge">运行正常</span>
                </div>
                <div class="card-content">
                    <div class="data-item">
                        <span class="data-label">特征向量</span>
                        <span class="data-value">[0.87, 0.45, 0.32]</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">分类结果</span>
                        <span class="data-value">正常</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">置信度</span>
                        <span class="data-value">76.2%</span>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>异常概率</span>
                            <span>24%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-normal" style="width: 24%"></div>
                        </div>
                    </div>
                    
                    <div class="data-item">
                        <span class="data-label">使用模型</span>
                        <span class="data-value">SVM分类器</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">更新时间</span>
                        <span class="data-value">14:28:42</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-brain"></i> 深度学习监测</h2>
                    <span class="card-status status-alarm-badge">故障报警</span>
                </div>
                <div class="card-content">
                    <div class="data-item">
                        <span class="data-label">模型输出</span>
                        <span class="data-value">故障 (外圈)</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">置信度</span>
                        <span class="data-value">94.7%</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">特征相似度</span>
                        <span class="data-value">0.89</span>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>故障概率</span>
                            <span>95%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-alarm" style="width: 95%"></div>
                        </div>
                    </div>
                    
                    <div class="data-item">
                        <span class="data-label">使用模型</span>
                        <span class="data-value">ResNet-1D</span>
                    </div>
                    <div class="data-item">
                        <span class="data-label">推理时间</span>
                        <span class="data-value">128 ms</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="history-section">
            <h2 class="section-title"><i class="fas fa-history"></i> 历史故障记录</h2>
            <table class="history-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>检测方法</th>
                        <th>故障类型</th>
                        <th>置信度</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2023-10-15 14:28:45</td>
                        <td>深度学习监测</td>
                        <td><span class="fault-type fault-outer">外圈损伤</span></td>
                        <td>94.7%</td>
                        <td><span class="status-alarm-badge">报警</span></td>
                    </tr>
                    <tr>
                        <td>2023-10-15 14:28:45</td>
                        <td>故障判断</td>
                        <td><span class="fault-type fault-outer">外圈损伤</span></td>
                        <td>86.3%</td>
                        <td><span class="status-alarm-badge">报警</span></td>
                    </tr>
                    <tr>
                        <td>2023-10-15 11:45:22</td>
                        <td>传统分类器</td>
                        <td><span class="fault-type fault-inner">内圈磨损</span></td>
                        <td>68.5%</td>
                        <td><span class="status-warning-badge">警告</span></td>
                    </tr>
                    <tr>
                        <td>2023-10-14 16:32:11</td>
                        <td>深度学习监测</td>
                        <td><span class="fault-type fault-cage">保持架断裂</span></td>
                        <td>82.1%</td>
                        <td><span class="status-alarm-badge">报警</span></td>
                    </tr>
                    <tr>
                        <td>2023-10-13 09:15:37</td>
                        <td>故障判断</td>
                        <td><span class="fault-type fault-inner">内圈点蚀</span></td>
                        <td>73.8%</td>
                        <td><span class="status-warning-badge">警告</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary"><i class="fas fa-sync-alt"></i> 刷新数据</button>
            <button class="btn btn-secondary"><i class="fas fa-file-pdf"></i> 生成报告</button>
            <button class="btn btn-alarm"><i class="fas fa-bell-slash"></i> 消音报警</button>
        </div>
        
        <div class="footer">
            <p>汽车轴承故障异常报警系统 v2.1 | 基于多算法融合的智能监测平台</p>
            <p>© 2023 智能制造技术中心 | 实时数据更新频率: 5秒</p>
        </div>
    </div>
    
    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const dateStr = now.toLocaleString('zh-CN');
            document.getElementById('lastUpdate').textContent = dateStr;
        }
        
        // 初始更新时间
        updateTime();
        
        // 每秒更新一次时间
        setInterval(updateTime, 1000);
        
        // 模拟状态变化（实际应用中从API获取数据）
        setInterval(() => {
            const statusBar = document.getElementById('statusBar');
            const random = Math.random();
            
            if (random > 0.7) {
                statusBar.className = 'status-bar alarm';
            } else if (random > 0.4) {
                statusBar.className = 'status-bar warning';
            } else {
                statusBar.className = 'status-bar normal';
            }
        }, 5000);
    </script>
</body>
</html>
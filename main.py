"""
地面数据分析决策系统 - 主程序入口
轴承故障诊断专业版
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from database.db_manager import DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class Application:
    """应用程序主类"""
    
    def __init__(self):
        self.app = None
        self.login_window = None
        self.main_window = None
        self.splash = None
        
    def create_splash_screen(self):
        """创建启动画面"""
        # 创建启动画面
        pixmap = QPixmap(600, 400)
        pixmap.fill(QColor(187, 244, 255))  # #bbf4ff 浅蓝色背景

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制标题
        painter.setPen(QColor(0, 102, 204))  # #0066cc 蓝色
        font = QFont("Microsoft YaHei", 26, QFont.Bold)
        painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignCenter,
                        "地面数据分析决策系统\n\n轴承故障诊断专业版\n\n正在启动...")

        painter.end()

        self.splash = QSplashScreen(pixmap)
        self.splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.splash.show()

        # 显示启动消息
        self.splash.showMessage("正在初始化系统...", Qt.AlignBottom | Qt.AlignCenter, QColor(0, 0, 0))
        
    def init_application(self):
        """初始化应用程序"""
        try:
            # 创建QApplication实例
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("地面数据分析决策系统")
            self.app.setApplicationVersion("1.0")
            self.app.setOrganizationName("轴承故障诊断实验室")
            
            # 设置应用程序图标（如果有的话）
            # self.app.setWindowIcon(QIcon("icon.ico"))
            
            # 创建启动画面
            self.create_splash_screen()
            
            # 处理事件，显示启动画面
            self.app.processEvents()
            
            logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"应用程序初始化失败: {e}")
            return False
    
    def show_login(self):
        """显示登录窗口"""
        try:
            self.splash.showMessage("正在加载登录界面...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
            self.app.processEvents()
            
            # 创建登录窗口
            self.login_window = LoginWindow()
            self.login_window.login_success.connect(self.on_login_success)
            
            # 延迟显示登录窗口，让启动画面显示一会儿
            QTimer.singleShot(2000, self.show_login_window)
            
        except Exception as e:
            logger.error(f"登录窗口创建失败: {e}")
            QMessageBox.critical(None, "错误", f"登录窗口创建失败:\n{e}")
            sys.exit(1)
    
    def show_login_window(self):
        """显示登录窗口"""
        if self.splash:
            self.splash.close()
        
        if self.login_window:
            self.login_window.show()
            # 将窗口移动到屏幕中央
            self.center_window(self.login_window)
    
    def center_window(self, window):
        """将窗口居中显示"""
        screen = self.app.desktop().screenGeometry()
        window_rect = window.geometry()
        x = (screen.width() - window_rect.width()) // 2
        y = (screen.height() - window_rect.height()) // 2
        window.move(x, y)
    
    def on_login_success(self, db_manager):
        """登录成功处理"""
        try:
            logger.info("用户登录成功，正在启动主界面")
            
            # 关闭登录窗口
            if self.login_window:
                self.login_window.close()
            
            # 创建主窗口
            self.main_window = MainWindow(db_manager)
            self.main_window.show()
            
            # 将主窗口居中显示
            self.center_window(self.main_window)
            
            logger.info("主界面启动成功")
            
        except Exception as e:
            logger.error(f"主界面启动失败: {e}")
            QMessageBox.critical(None, "错误", f"主界面启动失败:\n{e}")
            sys.exit(1)
    
    def run(self):
        """运行应用程序"""
        try:
            # 初始化应用程序
            if not self.init_application():
                return 1
            
            # 显示登录界面
            self.show_login()
            
            # 运行应用程序主循环
            return self.app.exec_()
            
        except Exception as e:
            logger.error(f"应用程序运行失败: {e}")
            if self.app:
                QMessageBox.critical(None, "严重错误", f"应用程序运行失败:\n{e}")
            return 1
        
        finally:
            # 清理资源
            logger.info("应用程序退出")


def check_dependencies():
    """检查依赖项"""
    try:
        import PyQt5
        import pymysql
        import pandas
        import numpy
        import matplotlib
        logger.info("所有依赖项检查通过")
        return True
    except ImportError as e:
        logger.error(f"缺少依赖项: {e}")
        print(f"错误: 缺少必要的依赖项 - {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def create_directories():
    """创建必要的目录"""
    directories = ['data', 'temp', 'exports', 'logs']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"创建目录: {directory}")


def main():
    """主函数"""
    print("=" * 50)
    print("地面数据分析决策系统")
    print("轴承故障诊断专业版 v1.0")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 创建必要目录
    create_directories()
    
    # 创建并运行应用程序
    app = Application()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())

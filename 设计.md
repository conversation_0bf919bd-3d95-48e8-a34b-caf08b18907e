# 地面数据分析决策系统 PyQt5 设计方案

## 整体设计理念
- **专业科技感**：深色主题配以高对比度元素
- **功能分区明确**：左侧导航 + 右侧工作区
- **现代化交互**：动画过渡、悬浮效果、卡片式布局
- **响应式设计**：适应不同分辨率

## 配色方案
```python
# 主色调
PRIMARY_BG = "#1e1e2e"       # 深蓝紫色背景
SECONDARY_BG = "#252535"     # 次级背景
ACCENT_COLOR = "#6c5ce7"     # 紫色强调色
HIGHLIGHT_COLOR = "#00cec9"  # 蓝绿色高亮

# 文字颜色
TEXT_PRIMARY = "#f5f6fa"     # 主文本白色
TEXT_SECONDARY = "#b2bec3"   # 次级文本灰色

# 功能色
SUCCESS_COLOR = "#00b894"    # 成功绿色
WARNING_COLOR = "#fdcb6e"    # 警告黄色
ERROR_COLOR = "#ff7675"      # 错误红色
INFO_COLOR = "#74b9ff"       # 信息蓝色

# 图表配色
CHART_COLORS = ["#6c5ce7", "#00cec9", "#fd79a8", "#fdcb6e", "#a29bfe"]
```

## 主界面布局设计
```markdown
### 主窗口结构 (QMainWindow)
┌──────────────────────────────┐
│  菜单栏 (QMenuBar)          │
├──────────────┬──────────────┤
│ 左侧导航栏   │              │
│ (QListWidget)│  主工作区     │
│              │ (QStackedWidget)│
│              │              │
│              │              │
└──────────────┴──────────────┘
```

## 功能模块设计

### 1. 登录界面
```markdown
#### 设计元素
- 居中卡片布局 (QWidget with QVBoxLayout)
- 应用Logo和标题
- 用户名/密码输入框 (QLineEdit)
- "记住我"复选框 (QCheckBox)
- 登录按钮 (QPushButton)

#### 交互效果
- 输入框获得焦点时边框高亮
- 登录按钮悬停动画
- 错误提示淡入效果

# 数据库配置
DATABASE_NAME = "music_db"
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '1234',
    'database': DATABASE_NAME
}

# TDMS表名
TDMS_TABLE_NAME = "tdms"

```

### 2. 主控制面板
```markdown
#### 左侧导航栏 (200px宽)
- 仪表盘 (带图标)
- 特征提取与分析
- 经典分类器监测
- 深度学习分类器监测
- 故障异常报警
- 检测报告生成
- 系统设置

#### 顶部状态栏
- 系统时间
- 数据库连接状态指示灯
- 用户信息
- 通知图标
```

### 3. 特征提取与分析模块
```markdown
#### 布局设计
┌──────────────────────────────┐
│ 信号选择: [下拉框] 振动/声音/温度 │
├──────────────┬──────────────┤
│ 特征类型选择 │  图表预览区   │
│ (卡片式按钮) │ (QGraphicsView)│
│              │              │
└──────────────┴──────────────┘
│  < 上一特征   下一特征 >     │
└──────────────────────────────┘

#### 功能元素
- 12种特征的可视化卡片 (带缩略图)
- 信号参数设置面板
- 实时计算指标显示
- 特征对比工具
- 导出图表按钮
```

### 4. 分类器监测模块
```markdown
#### 经典分类器界面
┌──────────────────────────────┐
│ [选择分类器] 下拉菜单          │
│ KNN | SVM | 随机森林 | ELM... │
├──────────────┬──────────────┤
│ 训练参数设置 │  训练过程可视化 │
│              │ (实时指标图表) │
├──────────────┴──────────────┤
│ [训练]按钮 [测试]按钮         │
├──────────────────────────────┤
│ 诊断结果展示区                │
│ - 混淆矩阵                   │
│ - 特征重要性                 │
│ - 故障类型分布               │
└──────────────────────────────┘

#### 深度学习界面
- 模型结构可视化 (使用Qt绘图)
- 训练进度3D柱状图
- 层激活可视化工具
- 注意力机制热力图
```

### 5. 故障报警面板
```markdown
#### 设计元素
- 设备状态概览 (卡片式布局)
┌─────────┬─────────┬─────────┐
│ 发动机  │ 变速箱  │ 减速器  │
│ ●正常   │ ●警告   │ ●故障   │
└─────────┴─────────┴─────────┘

- 实时数据流监视器
- 报警历史时间线
- 多级报警分类 (警告/严重/危急)

#### 交互功能
- 点击报警条目显示详细信息
- 报警确认按钮
- 报警屏蔽选项
```

### 6. 检测报告生成
```markdown
#### 报告编辑界面
┌──────────────────────────────┐
│ 报告模板选择 [下拉菜单]        │
├──────────────┬──────────────┤
│ 组件选择     │ 报告预览      │
│ (树形结构)   │ (QTextEdit)   │
├──────────────┴──────────────┤
│ [添加图表] [添加表格] [导出]  │
└──────────────────────────────┘

#### 功能特点
- 拖拽式组件添加
- 自动填充数据占位符
- 多格式导出 (PDF/HTML/DOCX)
- 历史报告版本管理
```

## 动画与交互设计
```python
# 使用QPropertyAnimation实现
1. 页面切换的滑动效果
2. 按钮悬停放大效果
3. 通知消息的淡入淡出
4. 数据加载的骨架屏动画
5. 图表数据的动态更新

# 使用QThread实现
1. 后台特征计算
2. 模型训练进度更新
3. 实时数据监控
```

## 技术实现要点
```markdown
### PyQt5核心组件
- QStackedWidget: 实现多页面切换
- QGraphicsView: 高性能图表渲染
- QTableView: 数据表格展示
- QThread: 后台任务处理
- QChart: 数据可视化

### 集成技术
- Matplotlib: 高级图表绘制
- PyMySQL: 数据库连接
- Pandas: 数据处理
- Scikit-learn: 经典机器学习
- TensorFlow/PyTorch: 深度学习集成

### 性能优化
1. 大数据集的懒加载
2. 图表渲染的离屏缓冲
3. 模型训练的进度分块更新
4. 内存敏感操作的资源监控
```

## 响应式设计策略
```markdown
1. 使用布局权重 (setStretch)
2. 关键组件的最小/最大尺寸设置
3. 字体大小相对单位 (em)
4. 高DPI屏幕支持
5. 折叠式侧边栏 (小屏幕自动隐藏)
```

## 示例界面代码结构
```python
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 窗口设置
        self.setWindowTitle("地面数据分析决策系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 应用样式
        self.apply_stylesheet()
        
        # 创建主布局
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航
        self.nav_bar = NavigationBar()
        main_layout.addWidget(self.nav_bar, 1)
        
        # 右侧工作区
        self.workspace = QStackedWidget()
        main_layout.addWidget(self.workspace, 4)
        
        # 添加功能页面
        self.workspace.addWidget(FeatureAnalysisPage())
        self.workspace.addWidget(ClassifierPage("经典"))
        self.workspace.addWidget(ClassifierPage("深度学习"))
        self.workspace.addWidget(AlarmDashboard())
        self.workspace.addWidget(ReportGenerator())
        
        self.setCentralWidget(main_widget)
        
        # 创建状态栏
        self.create_status_bar()
    
    def apply_stylesheet(self):
        # 加载QSS样式表
        style = f"""
        QMainWindow {{
            background-color: {PRIMARY_BG};
        }}
        /* 更多样式... */
        """
        self.setStyleSheet(style)
    
    def create_status_bar(self):
        # 状态栏实现
        pass
```

## 开发路线图
```mermaid
gantt
    title 地面数据分析决策系统开发计划
    dateFormat  YYYY-MM-DD
    section 核心框架
    登录模块           :done,    des1, 2023-07-01, 7d
    主界面架构         :active,  des2, 2023-07-08, 5d
    导航系统           :         des3, after des2, 3d
    
    section 功能模块
    特征提取分析       :         des4, after des3, 10d
    经典分类器监测     :         des5, after des4, 8d
    深度学习监测       :         des6, after des5, 12d
    报警系统           :         des7, after des6, 7d
    报告生成器         :         des8, after des7, 9d
    
    section 优化测试
    性能优化           :         des9, after des8, 5d
    UI美化            :         des10, after des9, 5d
    系统测试           :         des11, after des10, 7d
```

这个设计方案结合了现代UI设计原则与专业数据分析需求，通过PyQt5的强大功能实现了一个既美观又实用的地面数据分析决策系统。系统采用模块化设计，便于功能扩展和维护，同时通过精心设计的交互效果提升用户体验。
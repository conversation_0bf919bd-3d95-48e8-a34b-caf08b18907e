"""
信号处理算法模块
提供基础的信号处理功能
"""

import numpy as np
from scipy import signal
from scipy.fft import fft, fftfreq, ifft
from scipy.signal import hilbert, butter, filtfilt


class SignalProcessor:
    """信号处理器类"""
    
    def __init__(self, sample_rate=1000):
        """
        初始化信号处理器
        
        Args:
            sample_rate (float): 采样率，默认1000Hz
        """
        self.sample_rate = sample_rate
    
    def process(self, data, operation, **kwargs):
        """
        信号处理主接口
        
        Args:
            data (array): 输入信号数据
            operation (str): 处理操作类型
            **kwargs: 操作参数
            
        Returns:
            array: 处理后的信号
        """
        if operation == 'filter':
            return self.apply_filter(data, **kwargs)
        elif operation == 'normalize':
            return self.normalize(data, **kwargs)
        elif operation == 'detrend':
            return self.detrend(data, **kwargs)
        elif operation == 'window':
            return self.apply_window(data, **kwargs)
        elif operation == 'resample':
            return self.resample(data, **kwargs)
        else:
            raise ValueError(f"不支持的操作类型: {operation}")
    
    def apply_filter(self, data, filter_type='lowpass', cutoff=None, order=4):
        """
        应用滤波器
        
        Args:
            data (array): 输入信号
            filter_type (str): 滤波器类型 ('lowpass', 'highpass', 'bandpass', 'bandstop')
            cutoff (float or tuple): 截止频率
            order (int): 滤波器阶数
            
        Returns:
            array: 滤波后的信号
        """
        if cutoff is None:
            cutoff = self.sample_rate / 4  # 默认截止频率
        
        nyquist = self.sample_rate / 2
        
        if filter_type in ['lowpass', 'highpass']:
            normal_cutoff = cutoff / nyquist
            b, a = butter(order, normal_cutoff, btype=filter_type)
        elif filter_type in ['bandpass', 'bandstop']:
            if not isinstance(cutoff, (list, tuple)) or len(cutoff) != 2:
                raise ValueError("带通/带阻滤波器需要两个截止频率")
            normal_cutoff = [f / nyquist for f in cutoff]
            b, a = butter(order, normal_cutoff, btype=filter_type)
        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")
        
        return filtfilt(b, a, data)
    
    def normalize(self, data, method='zscore'):
        """
        信号归一化
        
        Args:
            data (array): 输入信号
            method (str): 归一化方法 ('zscore', 'minmax', 'robust')
            
        Returns:
            array: 归一化后的信号
        """
        if method == 'zscore':
            return (data - np.mean(data)) / np.std(data)
        elif method == 'minmax':
            return (data - np.min(data)) / (np.max(data) - np.min(data))
        elif method == 'robust':
            median = np.median(data)
            mad = np.median(np.abs(data - median))
            return (data - median) / mad
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
    
    def detrend(self, data, method='linear'):
        """
        去趋势
        
        Args:
            data (array): 输入信号
            method (str): 去趋势方法 ('linear', 'constant')
            
        Returns:
            array: 去趋势后的信号
        """
        return signal.detrend(data, type=method)
    
    def apply_window(self, data, window_type='hann'):
        """
        应用窗函数
        
        Args:
            data (array): 输入信号
            window_type (str): 窗函数类型
            
        Returns:
            array: 加窗后的信号
        """
        window = signal.get_window(window_type, len(data))
        return data * window
    
    def resample(self, data, new_sample_rate):
        """
        重采样
        
        Args:
            data (array): 输入信号
            new_sample_rate (float): 新的采样率
            
        Returns:
            array: 重采样后的信号
        """
        num_samples = int(len(data) * new_sample_rate / self.sample_rate)
        return signal.resample(data, num_samples)
    
    def calculate_envelope(self, data):
        """
        计算信号包络
        
        Args:
            data (array): 输入信号
            
        Returns:
            array: 信号包络
        """
        analytic_signal = hilbert(data)
        return np.abs(analytic_signal)
    
    def calculate_instantaneous_frequency(self, data):
        """
        计算瞬时频率
        
        Args:
            data (array): 输入信号
            
        Returns:
            array: 瞬时频率
        """
        analytic_signal = hilbert(data)
        instantaneous_phase = np.unwrap(np.angle(analytic_signal))
        instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.sample_rate
        return instantaneous_frequency
    
    def segment_signal(self, data, segment_length, overlap=0.5):
        """
        信号分段
        
        Args:
            data (array): 输入信号
            segment_length (int): 段长度
            overlap (float): 重叠比例 (0-1)
            
        Returns:
            list: 信号段列表
        """
        step = int(segment_length * (1 - overlap))
        segments = []
        
        for i in range(0, len(data) - segment_length + 1, step):
            segment = data[i:i + segment_length]
            segments.append(segment)
        
        return segments
    
    def calculate_snr(self, signal_data, noise_data=None):
        """
        计算信噪比
        
        Args:
            signal_data (array): 信号数据
            noise_data (array): 噪声数据，如果为None则估算噪声
            
        Returns:
            float: 信噪比 (dB)
        """
        signal_power = np.mean(signal_data ** 2)
        
        if noise_data is None:
            # 估算噪声：使用高频部分作为噪声估计
            fft_data = fft(signal_data)
            freqs = fftfreq(len(signal_data), 1/self.sample_rate)
            high_freq_mask = np.abs(freqs) > self.sample_rate / 4
            noise_fft = fft_data.copy()
            noise_fft[~high_freq_mask] = 0
            noise_signal = np.real(ifft(noise_fft))
            noise_power = np.mean(noise_signal ** 2)
        else:
            noise_power = np.mean(noise_data ** 2)
        
        if noise_power == 0:
            return float('inf')
        
        snr = 10 * np.log10(signal_power / noise_power)
        return snr
    
    def detect_peaks(self, data, height=None, distance=None, prominence=None):
        """
        峰值检测
        
        Args:
            data (array): 输入信号
            height (float): 最小峰值高度
            distance (int): 峰值间最小距离
            prominence (float): 峰值突出度
            
        Returns:
            tuple: (峰值索引, 峰值属性)
        """
        peaks, properties = signal.find_peaks(data, height=height, distance=distance, prominence=prominence)
        return peaks, properties
    
    def calculate_rms(self, data):
        """
        计算均方根值
        
        Args:
            data (array): 输入信号
            
        Returns:
            float: RMS值
        """
        return np.sqrt(np.mean(data ** 2))
    
    def calculate_crest_factor(self, data):
        """
        计算峰值因子
        
        Args:
            data (array): 输入信号
            
        Returns:
            float: 峰值因子
        """
        rms = self.calculate_rms(data)
        if rms == 0:
            return 0
        return np.max(np.abs(data)) / rms

# 故障异常报警系统

## 系统概述

故障异常报警系统是轴承故障诊断系统的核心模块之一，基于多算法融合的实时监测与预警平台。系统参考了现代化的Web界面设计，同时保持与原系统配色方案的一致性。

## 功能特点

### 🔴 实时状态监控
- **状态栏显示**：实时显示系统当前状态（正常/警告/报警）
- **视觉指示器**：使用颜色和图标直观显示系统状态
- **设备信息**：显示设备ID和最后更新时间
- **自动更新**：每秒更新时间，每5秒更新状态数据

### 📊 多算法监测卡片
系统包含三个主要监测模块，每个模块以卡片形式展示：

#### 1. 故障判断卡片 🔍
- **振动分析**：显示振动幅度、温度变化、噪声水平
- **故障概率**：以进度条形式显示故障概率
- **故障类型**：识别具体故障类型（外圈损伤、内圈磨损等）
- **置信度**：显示检测结果的可信度

#### 2. 传统分类器监测卡片 ⚙️
- **特征向量**：显示提取的特征向量
- **分类结果**：传统机器学习算法的分类结果
- **异常概率**：显示异常检测概率
- **模型信息**：显示使用的分类器类型（SVM、随机森林等）

#### 3. 深度学习监测卡片 🧠
- **模型输出**：深度学习模型的预测结果
- **特征相似度**：与已知故障模式的相似度
- **故障概率**：深度学习模型预测的故障概率
- **性能指标**：显示推理时间和使用的模型类型

### 📋 历史故障记录
- **独立子页面**：点击按钮跳转到专门的历史记录页面
- **数据筛选**：支持按日期范围和故障类型筛选
- **详细记录**：包含时间、检测方法、故障类型、置信度等信息
- **导出功能**：支持导出历史记录（预留功能）

### 🎛️ 控制功能
- **🔄 刷新数据**：手动刷新监测数据
- **📋 历史故障记录**：打开历史记录子页面
- **🔇 消音报警**：停止声音报警（保留视觉报警）
- **📄 生成报告**：生成详细的故障诊断报告

## 设计特点

### 🎨 视觉设计
- **配色方案**：采用原系统的浅蓝色配色方案
  - 主背景：`#bbf4ff` (浅蓝色)
  - 次级背景：`#a8e6ff`
  - 强调色：`#0066cc` (蓝色)
  - 成功色：`#00b894` (绿色)
  - 警告色：`#fdcb6e` (黄色)
  - 错误色：`#ff7675` (红色)

- **布局设计**：参考HTML模板的现代化排版
  - 卡片式布局
  - 响应式设计
  - 悬浮效果
  - 圆角边框

### 🔧 技术实现
- **框架**：基于PyQt5开发
- **架构**：模块化设计，易于扩展
- **数据更新**：使用QTimer实现实时数据更新
- **状态管理**：统一的状态管理机制
- **事件处理**：完整的信号槽机制

## 使用方法

### 环境要求
- Python 3.7+
- PyQt5
- DL环境（推荐）

### 运行方式

#### 1. 在DL环境中运行（推荐）
```bash
conda activate DL
python run_fault_alarm_dl.py
```

#### 2. 直接运行测试
```bash
python test_fault_alarm.py
```

#### 3. 在主系统中使用
故障异常报警系统已集成到主窗口中，可通过导航栏的"⚠️ 故障异常报警"选项访问。

### 操作说明

1. **查看实时状态**
   - 观察顶部状态栏的颜色和图标
   - 绿色边框表示正常，黄色表示警告，红色表示报警

2. **监控各算法结果**
   - 查看三个监测卡片的状态标签
   - 观察进度条显示的概率值
   - 注意置信度和具体检测结果

3. **查看历史记录**
   - 点击"📋 历史故障记录"按钮
   - 在弹出窗口中筛选和查看历史数据
   - 可按日期范围和故障类型筛选

4. **控制操作**
   - 使用"🔄 刷新数据"手动更新数据
   - 使用"🔇 消音报警"停止声音提醒
   - 使用"📄 生成报告"创建诊断报告

## 扩展功能

### 数据源集成
- 可连接实际传感器数据
- 支持TDMS文件数据源
- 可集成数据库存储

### 报警机制
- 支持多级报警阈值设置
- 可配置声音和视觉报警
- 支持邮件和短信通知

### 报告生成
- 自动生成PDF报告
- 支持自定义报告模板
- 包含图表和统计分析

## 文件结构

```
ui/
├── fault_alarm_system.py      # 主要模块文件
├── __init__.py               # 模块导入
└── styles.py                 # 样式定义

test_fault_alarm.py           # 测试脚本
run_fault_alarm_dl.py         # DL环境运行脚本
故障异常报警系统说明.md        # 本说明文档
```

## 注意事项

1. **环境配置**：建议在DL环境中运行以确保所有依赖正确安装
2. **数据模拟**：当前版本使用模拟数据，实际部署时需要连接真实数据源
3. **性能优化**：大量数据时建议优化更新频率和显示内容
4. **扩展开发**：可根据实际需求添加更多监测算法和功能

## 更新日志

- **v1.0** (2024-08-01)
  - 初始版本发布
  - 实现基本的故障异常报警功能
  - 集成三种监测算法
  - 添加历史记录查看功能
  - 采用原系统配色方案

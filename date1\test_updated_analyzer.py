#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的TDMS分析器
验证复选框选择、字体大小、多结果显示和保存功能
"""

import tkinter as tk
from tdms_analyzer import TDMSAnalyzer
import os

def test_analyzer():
    """测试分析器功能"""
    print("启动TDMS分析器...")
    print("功能验证清单：")
    print("1. 复选框选择界面")
    print("2. 调大的字体显示")
    print("3. 多分析结果同时显示")
    print("4. 保存图片功能")
    print("5. 清除结果功能")
    
    # 创建主窗口
    root = tk.Tk()
    
    # 创建分析器实例
    analyzer = TDMSAnalyzer(root)
    
    # 运行程序
    root.mainloop()

if __name__ == "__main__":
    test_analyzer() 
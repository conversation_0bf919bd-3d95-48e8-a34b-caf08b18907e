#!/usr/bin/env python3
"""
测试登录界面
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.login_window import LoginWindow

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建登录窗口
    login_window = LoginWindow()
    login_window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

"""
测试改进后的特征提取与分析界面
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.feature_analysis import FeatureAnalysis


class MockDatabaseManager:
    """模拟数据库管理器"""
    def __init__(self):
        pass

    def get_tdms_files(self):
        return []


class TestWindow(QMainWindow):
    """测试窗口"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("特征提取与分析界面测试 - 改进版")
        self.setGeometry(100, 100, 1400, 900)

        # 创建模拟数据库管理器
        db_manager = MockDatabaseManager()

        # 创建特征分析界面
        self.feature_analysis = FeatureAnalysis(db_manager)

        # 设置为中央控件
        self.setCentralWidget(self.feature_analysis)

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #bbf4ff;
            }
        """)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("特征分析界面测试")
    app.setApplicationVersion("1.0")
    
    # 创建并显示测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

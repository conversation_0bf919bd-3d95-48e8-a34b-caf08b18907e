#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多特征马氏距离分类子界面
验证子界面的创建和功能是否正常
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mahalanobis_dialog():
    """测试马氏距离分类子界面"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("马氏距离分类子界面测试")
    main_window.resize(400, 300)
    
    # 创建中央部件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    
    # 创建测试按钮
    test_btn = QPushButton("测试多特征马氏距离分类子界面")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c5ce7;
            color: white;
            font-size: 14px;
            font-weight: 500;
            min-height: 40px;
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
        QPushButton:hover {
            background-color: #5a4fcf;
        }
        QPushButton:pressed {
            background-color: #4834d4;
        }
    """)
    
    def open_dialog():
        """打开马氏距离分类子界面"""
        try:
            from ui.fault_diagnosis import MultiFeatureMahalanobisDialog
            
            # 创建测试特征数据
            test_features = {
                '均值': 0.05,
                '标准差': 0.8,
                '方差': 0.64,
                '峰值因子': 1.5,
                '偏度': 0.2,
                '峰度': 3.2
            }
            
            # 创建并显示子界面
            dialog = MultiFeatureMahalanobisDialog(main_window, test_features)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("用户关闭了子界面，获取分析结果...")
                results = dialog.get_results()
                if results:
                    print(f"分类结果: {results.get('classification_result', 'unknown')}")
                    print(f"到正样本距离: {results.get('mahalanobis_distance_positive', 'N/A')}")
                    print(f"到负样本距离: {results.get('mahalanobis_distance_negative', 'N/A')}")
                else:
                    print("未获取到分析结果")
            else:
                print("用户取消了操作")
                
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    test_btn.clicked.connect(open_dialog)
    layout.addWidget(test_btn)
    
    # 添加说明文本
    info_label = QPushButton("点击按钮测试马氏距离分类子界面")
    info_label.setEnabled(False)
    info_label.setStyleSheet("""
        QPushButton {
            background-color: transparent;
            color: #666;
            font-size: 12px;
            border: none;
            font-family: 'Microsoft YaHei';
        }
    """)
    layout.addWidget(info_label)
    
    # 显示主窗口
    main_window.show()
    
    # 运行应用程序
    return app.exec_()

if __name__ == "__main__":
    print("马氏距离分类子界面测试")
    print("=" * 50)
    print("这个测试将创建一个简单的主窗口，")
    print("点击按钮可以打开马氏距离分类子界面。")
    print("子界面将显示分析过程和结果图表。")
    print("关闭子界面后，结果将在控制台显示。")
    print("=" * 50)
    
    try:
        exit_code = test_mahalanobis_dialog()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

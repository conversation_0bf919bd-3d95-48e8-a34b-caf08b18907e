"""
深度学习算法模块
提供PyTorch深度学习算法的统一接口
"""

import numpy as np
import os
from typing import Dict, Any, Optional, Callable, Tuple, List
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

# PyTorch相关导入
PYTORCH_AVAILABLE = False
torch = None
nn = None
optim = None
F = None
DataLoader = None
TensorDataset = None

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    from torch.utils.data import DataLoader, TensorDataset
    PYTORCH_AVAILABLE = True
except ImportError:
    # 创建虚拟的PyTorch模块以避免NameError
    class DummyTorch:
        @staticmethod
        def tensor(*args, **kwargs):
            return None
        @staticmethod
        def save(obj, path):
            pass
        @staticmethod
        def load(path, map_location=None):
            return None
        @staticmethod
        def cuda():
            class Device:
                @staticmethod
                def is_available():
                    return False
            return Device()
        @staticmethod
        def device(device_name):
            return device_name
        @staticmethod
        def FloatTensor(*args, **kwargs):
            return None
        @staticmethod
        def LongTensor(*args, **kwargs):
            return None
        @staticmethod
        def max(*args, **kwargs):
            return None, None
    
    class DummyNN:
        class Module:
            def __init__(self):
                pass
            def forward(self, x):
                return x
            def train(self):
                pass
            def eval(self):
                pass
            def parameters(self):
                return []
            def to(self, device):
                return self
            def state_dict(self):
                return {}
            def load_state_dict(self, state_dict):
                pass
        
        class Sequential:
            def __init__(self, *args):
                pass
            def forward(self, x):
                return x
        
        class Linear:
            def __init__(self, *args, **kwargs):
                pass
        
        class ReLU:
            def __init__(self, *args, **kwargs):
                pass
        
        class Dropout:
            def __init__(self, *args, **kwargs):
                pass
        
        class CrossEntropyLoss:
            def __init__(self, *args, **kwargs):
                pass
            def __call__(self, *args, **kwargs):
                return 0
    
    class DummyOptim:
        class Adam:
            def __init__(self, *args, **kwargs):
                pass
            def zero_grad(self):
                pass
            def step(self):
                pass
    
    class DummyF:
        @staticmethod
        def softmax(*args, **kwargs):
            return None
    
    class DummyDataLoader:
        def __init__(self, *args, **kwargs):
            pass
        def __iter__(self):
            return iter([])
    
    class DummyTensorDataset:
        def __init__(self, *args, **kwargs):
            pass
    
    torch = DummyTorch()
    nn = DummyNN()
    optim = DummyOptim()
    F = DummyF()
    DataLoader = DummyDataLoader
    TensorDataset = DummyTensorDataset


class MLPModel(nn.Module if PYTORCH_AVAILABLE else object):
    """多层感知机模型"""
    
    def __init__(self, input_size: int, hidden_units: List[int], n_classes: int, dropout_rate: float = 0.3):
        if PYTORCH_AVAILABLE:
            super(MLPModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_units = hidden_units
        self.n_classes = n_classes
        self.dropout_rate = dropout_rate
        
        if PYTORCH_AVAILABLE:
            layers = []
            prev_size = input_size
            
            # 添加隐藏层
            for units in hidden_units:
                layers.append(nn.Linear(prev_size, units))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout_rate))
                prev_size = units
            
            # 添加输出层
            layers.append(nn.Linear(prev_size, n_classes))
            
            self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        if PYTORCH_AVAILABLE:
            return self.network(x)
        return x


class DeepLearningClassifier:
    """深度学习分类器"""
    
    def __init__(self):
        """初始化分类器"""
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.device = None
        self.model_configs = self._get_default_configs()
        self.training_history = []
        
        if PYTORCH_AVAILABLE:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def _get_default_configs(self) -> Dict[str, Dict]:
        """获取默认模型配置"""
        return {
            '简单MLP': {
                'hidden_units': [128, 64],
                'dropout_rate': 0.3,
                'learning_rate': 0.001,
                'epochs': 100,
                'batch_size': 32
            },
            '深层MLP': {
                'hidden_units': [256, 128, 64, 32],
                'dropout_rate': 0.4,
                'learning_rate': 0.001,
                'epochs': 150,
                'batch_size': 32
            },
            '宽层MLP': {
                'hidden_units': [512, 256],
                'dropout_rate': 0.5,
                'learning_rate': 0.0005,
                'epochs': 100,
                'batch_size': 64
            }
        }
    
    def prepare_data(self, X: np.ndarray, y: np.ndarray, 
                    test_size: float = 0.2, 
                    random_state: int = 42,
                    scaling_method: str = 'standard') -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            X: 特征数据
            y: 标签数据
            test_size: 测试集比例
            random_state: 随机种子
            scaling_method: 缩放方法 ('standard', 'minmax', 'none')
            
        Returns:
            X_train, X_test, y_train, y_test
        """
        if not PYTORCH_AVAILABLE:
            raise RuntimeError("PyTorch未安装，无法使用深度学习功能")
        
        # 标签编码
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=test_size, random_state=random_state, stratify=y_encoded
        )
        
        # 数据缩放
        if scaling_method == 'standard':
            from sklearn.preprocessing import StandardScaler
            self.scaler = StandardScaler()
            X_train = self.scaler.fit_transform(X_train)
            X_test = self.scaler.transform(X_test)
        elif scaling_method == 'minmax':
            from sklearn.preprocessing import MinMaxScaler
            self.scaler = MinMaxScaler()
            X_train = self.scaler.fit_transform(X_train)
            X_test = self.scaler.transform(X_test)
        
        return X_train, X_test, y_train, y_test
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
             architecture: str, config: Dict[str, Any],
             progress_callback: Optional[Callable] = None,
             epoch_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X_train: 训练特征
            y_train: 训练标签
            architecture: 网络架构名称
            config: 模型配置
            progress_callback: 进度回调函数
            epoch_callback: 每个epoch完成时的回调函数
            
        Returns:
            训练结果字典
        """
        if not PYTORCH_AVAILABLE:
            raise RuntimeError("PyTorch未安装，无法使用深度学习功能")
        
        if progress_callback:
            progress_callback(10, "构建神经网络...")
        
        # 获取配置
        model_config = self.model_configs.get(architecture, self.model_configs['简单MLP']).copy()
        model_config.update(config)
        
        # 构建模型
        input_size = X_train.shape[1]
        n_classes = len(np.unique(y_train))
        
        self.model = MLPModel(
            input_size=input_size,
            hidden_units=model_config['hidden_units'],
            n_classes=n_classes,
            dropout_rate=model_config['dropout_rate']
        )
        
        self.model.to(self.device)
        
        if progress_callback:
            progress_callback(20, "准备数据...")
        
        # 转换数据为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.LongTensor(y_train).to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=model_config['batch_size'], shuffle=True)
        
        # 定义损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=model_config['learning_rate'])
        
        if progress_callback:
            progress_callback(30, "开始训练...")
        
        # 训练历史记录
        train_losses = []
        train_accuracies = []
        
        # 训练循环
        epochs = model_config['epochs']
        for epoch in range(epochs):
            self.model.train()
            running_loss = 0.0
            correct = 0
            total = 0
            
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                running_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()
            
            epoch_loss = running_loss / len(train_loader)
            epoch_acc = correct / total
            
            train_losses.append(epoch_loss)
            train_accuracies.append(epoch_acc)
            
            # 调用epoch回调
            if epoch_callback:
                epoch_callback(epoch + 1, epoch_loss, epoch_acc, 0.0, 0.0)
            
            # 更新进度
            progress = 30 + int((epoch + 1) / epochs * 60)
            if progress_callback:
                progress_callback(progress, f"训练中... Epoch {epoch + 1}/{epochs}")
        
        if progress_callback:
            progress_callback(100, "训练完成!")
        
        # 记录训练历史
        training_record = {
            'architecture': architecture,
            'config': model_config,
            'train_losses': train_losses,
            'train_accuracies': train_accuracies,
            'training_samples': len(X_train),
            'features': X_train.shape[1],
            'classes': n_classes
        }
        self.training_history.append(training_record)
        
        return training_record

    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """
        评估模型

        Args:
            X_test: 测试特征
            y_test: 测试标签

        Returns:
            评估结果字典
        """
        if not PYTORCH_AVAILABLE:
            raise RuntimeError("PyTorch未安装，无法使用深度学习功能")

        if self.model is None:
            raise ValueError("模型尚未训练")

        # 转换数据
        X_test_tensor = torch.FloatTensor(X_test).to(self.device)

        # 评估模型
        self.model.eval()
        with torch.no_grad():
            test_outputs = self.model(X_test_tensor)
            y_pred_proba = F.softmax(test_outputs, dim=1).cpu().numpy()
            y_pred_classes = torch.argmax(test_outputs, dim=1).cpu().numpy()

        # 计算性能指标
        accuracy = accuracy_score(y_test, y_pred_classes)
        precision = precision_score(y_test, y_pred_classes, average='weighted')
        recall = recall_score(y_test, y_pred_classes, average='weighted')
        f1 = f1_score(y_test, y_pred_classes, average='weighted')
        cm = confusion_matrix(y_test, y_pred_classes)

        # 分类报告
        class_names = self.label_encoder.classes_ if self.label_encoder else None
        report = classification_report(y_test, y_pred_classes, target_names=class_names, output_dict=True)

        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': cm,
            'classification_report': report,
            'y_test': y_test,
            'y_pred': y_pred_classes,
            'y_pred_proba': y_pred_proba,
            'class_names': class_names
        }

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        预测新数据

        Args:
            X: 特征数据

        Returns:
            预测标签和概率
        """
        if not PYTORCH_AVAILABLE:
            raise RuntimeError("PyTorch未安装，无法使用深度学习功能")

        if self.model is None:
            raise ValueError("模型尚未训练")

        # 数据预处理
        if self.scaler:
            X = self.scaler.transform(X)

        # 转换为张量
        X_tensor = torch.FloatTensor(X).to(self.device)

        # 预测
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(X_tensor)
            y_pred_proba = F.softmax(outputs, dim=1).cpu().numpy()
            y_pred_classes = torch.argmax(outputs, dim=1).cpu().numpy()

        # 标签解码
        if self.label_encoder:
            y_pred_classes = self.label_encoder.inverse_transform(y_pred_classes)

        return y_pred_classes, y_pred_proba

    def save_model(self, filepath: str) -> None:
        """
        保存模型

        Args:
            filepath: 保存路径
        """
        if not PYTORCH_AVAILABLE:
            raise RuntimeError("PyTorch未安装，无法使用深度学习功能")

        if self.model is None:
            raise ValueError("模型尚未训练")

        model_data = {
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'input_size': self.model.input_size,
                'hidden_units': self.model.hidden_units,
                'n_classes': self.model.n_classes,
                'dropout_rate': self.model.dropout_rate
            },
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'training_history': self.training_history
        }

        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        torch.save(model_data, filepath)

    def load_model(self, filepath: str) -> None:
        """
        加载模型

        Args:
            filepath: 模型路径
        """
        if not PYTORCH_AVAILABLE:
            raise RuntimeError("PyTorch未安装，无法使用深度学习功能")

        if not os.path.exists(filepath):
            raise FileNotFoundError(f"模型文件不存在: {filepath}")

        model_data = torch.load(filepath, map_location=self.device)

        # 重建模型
        model_config = model_data['model_config']
        self.model = MLPModel(
            input_size=model_config['input_size'],
            hidden_units=model_config['hidden_units'],
            n_classes=model_config['n_classes'],
            dropout_rate=model_config['dropout_rate']
        )

        self.model.load_state_dict(model_data['model_state_dict'])
        self.model.to(self.device)

        self.scaler = model_data.get('scaler')
        self.label_encoder = model_data.get('label_encoder')
        self.training_history = model_data.get('training_history', [])

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model is None:
            return {}

        info = {
            'architecture': 'MLP',
            'input_size': self.model.input_size,
            'hidden_units': self.model.hidden_units,
            'n_classes': self.model.n_classes,
            'dropout_rate': self.model.dropout_rate,
            'device': str(self.device) if self.device else 'cpu',
            'training_history': self.training_history
        }

        return info

    def is_available(self) -> bool:
        """检查PyTorch是否可用"""
        return PYTORCH_AVAILABLE

"""
登录窗口模块
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QCheckBox, QFrame,
                             QApplication, QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QThread, QParallelAnimationGroup
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QBrush
from database.db_manager import DatabaseManager
from ui.styles import get_login_stylesheet, ACCENT_COLOR, ERROR_COLOR, SUCCESS_COLOR


class DatabaseTestThread(QThread):
    """数据库连接测试线程"""
    result_ready = pyqtSignal(bool, str)
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
    
    def run(self):
        """执行数据库连接测试"""
        success, message = self.db_manager.test_connection()
        self.result_ready.emit(success, message)


class LoginWindow(QWidget):
    """登录窗口"""
    login_success = pyqtSignal(object)  # 登录成功信号，传递数据库管理器对象
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.test_thread = None
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("登录系统")
        self.setMinimumSize(500, 700)  # 调整窗口大小
        self.resize(500, 700)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 应用样式表
        self.setStyleSheet(get_login_stylesheet())
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setAlignment(Qt.AlignCenter)
        main_layout.setSpacing(10)

        # 添加顶部logo区域
        self.create_login_logo_area(main_layout)
        
        # 创建登录卡片
        self.login_card = QFrame()
        self.login_card.setObjectName("login_card")
        self.login_card.setFixedSize(450, 650)
        self.login_card.setStyleSheet("""
            QFrame#login_card {
                background: white;
                border-radius: 20px;
                border: none;
            }
        """)

        card_layout = QVBoxLayout(self.login_card)
        card_layout.setSpacing(0)
        card_layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题区域 - 渐变背景头部
        header_widget = QWidget()
        header_widget.setFixedHeight(200)
        header_widget.setStyleSheet("""
            QWidget {
                background: #bbf4ff;
                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
            }
        """)

        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(30, 40, 30, 30)
        header_layout.setSpacing(10)
        header_layout.setAlignment(Qt.AlignCenter)

        # 主标题
        title_label = QLabel("登录系统")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 42px;
                font-weight: 600;
                color: #000000;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                letter-spacing: 2px;
                margin: 0;
                padding: 0;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("欢迎回来，请登录您的账户")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 400;
                color: #333333;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                letter-spacing: 1px;
                margin: 0;
                padding: 0;
                background: transparent;
            }
        """)
        header_layout.addWidget(subtitle_label)

        card_layout.addWidget(header_widget)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setContentsMargins(30, 30, 30, 20)
        form_layout.setSpacing(20)

        # 用户名区域
        username_label = QLabel("用户名")
        username_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: 500;
                color: #333;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 0;
                padding: 0 0 0 5px;
            }
        """)
        form_layout.addWidget(username_label)

        # 用户名输入框容器
        username_container = QWidget()
        username_container.setFixedHeight(60)
        username_container.setStyleSheet("""
            QWidget {
                background: white;
                border: 2px solid #ddd;
                border-radius: 10px;
            }
            QWidget:focus-within {
                border: 2px solid #0066cc;
            }
        """)

        username_container_layout = QHBoxLayout(username_container)
        username_container_layout.setContentsMargins(15, 0, 15, 0)
        username_container_layout.setSpacing(10)

        # 用户图标
        user_icon = QLabel("👤")
        user_icon.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: #999;
                background: transparent;
                border: none;
            }
        """)
        username_container_layout.addWidget(user_icon)

        # 用户名输入框
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setStyleSheet("""
            QLineEdit {
                font-size: 18px;
                font-family: 'Microsoft YaHei', sans-serif;
                padding: 0;
                border: none;
                background: transparent;
                color: #333;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: normal;
            }
        """)
        username_container_layout.addWidget(self.username_input)
        form_layout.addWidget(username_container)

        # 密码区域
        password_label = QLabel("密码")
        password_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: 500;
                color: #333;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 0;
                padding: 0 0 -2px 5px;
            }
        """)
        form_layout.addWidget(password_label)

        # 密码输入框容器
        password_container = QWidget()
        password_container.setFixedHeight(60)
        password_container.setStyleSheet("""
            QWidget {
                background: white;
                border: 2px solid #ddd;
                border-radius: 10px;
            }
            QWidget:focus-within {
                border: 2px solid #0066cc;
            }
        """)

        password_container_layout = QHBoxLayout(password_container)
        password_container_layout.setContentsMargins(15, 0, 15, 0)
        password_container_layout.setSpacing(10)

        # 密码图标
        lock_icon = QLabel("🔒")
        lock_icon.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: #999;
                background: transparent;
                border: none;
            }
        """)
        password_container_layout.addWidget(lock_icon)

        # 密码输入框
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                font-size: 18px;
                font-family: 'Microsoft YaHei', sans-serif;
                padding: 0;
                border: none;
                background: transparent;
                color: #333;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: normal;
            }
        """)
        password_container_layout.addWidget(self.password_input)

        # 密码可见切换按钮
        self.password_toggle_btn = QPushButton("👁")
        self.password_toggle_btn.setFixedSize(30, 30)
        self.password_toggle_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                font-size: 20px;
                color: #999;
                border-radius: 15px;
            }
            QPushButton:hover {
                color: #0066cc;
            }
        """)
        self.password_toggle_btn.clicked.connect(self.toggle_password_visibility)
        password_container_layout.addWidget(self.password_toggle_btn)

        form_layout.addWidget(password_container)

        # 记住我和忘记密码区域
        options_layout = QHBoxLayout()
        options_layout.setContentsMargins(0, 0, 0, 0)

        self.remember_checkbox = QCheckBox("记住登录信息")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 18px;
                font-family: 'Microsoft YaHei', sans-serif;
                color: #666;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background: white;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #0066cc;
            }
            QCheckBox::indicator:checked {
                background: #0066cc;
                border: 2px solid #0066cc;
            }
        """)
        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()

        # 忘记密码链接
        forgot_password_label = QLabel('<a href="#" style="color: #0066cc; text-decoration: none;">忘记密码?</a>')
        forgot_password_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-family: 'Microsoft YaHei', sans-serif;
                padding: 8px 5px 14px 12px;
                
            }
            QLabel a {
                color: #0066cc;
                text-decoration: none;
            }
            QLabel a:hover {
                text-decoration: underline;
            }
        """)
        options_layout.addWidget(forgot_password_label)

        form_layout.addLayout(options_layout)

        # 登录按钮
        self.login_button = QPushButton("登 录")
        self.login_button.setFixedHeight(60)
        self.login_button.setStyleSheet("""
            QPushButton {
                font-size: 24px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                border: none;
                border-radius: 10px;
                background: #0066cc;
                color: white;
                letter-spacing: 4px;
            }
            QPushButton:hover {
                background: #0052a3;
            }
            QPushButton:pressed {
                background: #004080;
            }
            QPushButton:disabled {
                background: #ccc;
                color: #999;
            }
        """)
        self.login_button.clicked.connect(self.login)
        form_layout.addWidget(self.login_button)

        card_layout.addWidget(form_widget)

        # 底部区域
        footer_widget = QWidget()
        footer_layout = QVBoxLayout(footer_widget)
        footer_layout.setContentsMargins(20, 20, 20, 20)
        footer_layout.setSpacing(15)

        # 注册链接
        register_layout = QHBoxLayout()
        register_layout.setAlignment(Qt.AlignCenter)

        register_text = QLabel("还没有账户？")
        register_text.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-family: 'Microsoft YaHei', sans-serif;
                color: #666;
            }
        """)
        register_layout.addWidget(register_text)

        register_link = QLabel('<a href="#" style="color: #4169E1; text-decoration: none; font-weight: 500;">立即注册</a>')
        register_link.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            QLabel a {
                color: #4169E1;
                text-decoration: none;
                font-weight: 500;
            }
            QLabel a:hover {
                text-decoration: underline;
            }
        """)
        register_layout.addWidget(register_link)

        footer_layout.addLayout(register_layout)

        # 版权信息
        copyright_label = QLabel("© 2023 系统管理平台. 保留所有权利")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-family: 'Microsoft YaHei', sans-serif;
                color: #999;
            }
        """)
        footer_layout.addWidget(copyright_label)

        card_layout.addWidget(footer_widget)

        # 添加登录卡片到主布局
        main_layout.addWidget(self.login_card)

        # 状态标签（在卡片外部）
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-family: 'Microsoft YaHei', sans-serif;
                font-weight: 500;
                padding: 8px 15px;
                border-radius: 8px;
                background: transparent;
                min-height: 20px;
                color: white;
            }
        """)
        main_layout.addWidget(self.status_label)

        # 进度条（在卡片外部）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-align: center;
                font-size: 12px;
                font-family: 'Microsoft YaHei', sans-serif;
                font-weight: 500;
                height: 16px;
            }
            QProgressBar::chunk {
                background: #4169E1;
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(self.progress_bar)

        self.setLayout(main_layout)
        
        # 设置默认值
        self.username_input.setText("admin")
        self.password_input.setText("admin")
        
        # 绑定回车键
        self.password_input.returnPressed.connect(self.login)
        self.username_input.returnPressed.connect(self.password_input.setFocus)

        # 密码可见性状态
        self.password_visible = False

        # 为输入框添加焦点事件
        self.username_input.installEventFilter(self)
        self.password_input.installEventFilter(self)

    def create_login_logo_area(self, parent_layout):
        """创建登录界面的logo区域"""
        # logo容器
        logo_container = QWidget()
        logo_container.setFixedHeight(60)
        logo_container.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)

        logo_layout = QHBoxLayout(logo_container)
        logo_layout.setContentsMargins(0, 10, 20, 10)
        logo_layout.setSpacing(15)

        # 左侧弹性空间
        logo_layout.addStretch()

        # 公司标注 - 文字在左边
        company_label = QLabel("解放军七四三五工厂")
        company_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333333 !important;
                font-family: 'Microsoft YaHei', 'SimHei', 'Arial Unicode MS', sans-serif;
                background-color: transparent;
                padding: 5px;
                min-width: 160px;
                max-width: 180px;
            }
        """)
        company_label.setAlignment(Qt.AlignCenter)
        company_label.setVisible(True)
        logo_layout.addWidget(company_label)

        # logo图标 - 在右边，使用本地图片
        logo_icon = QLabel()
        logo_icon.setAlignment(Qt.AlignCenter)
        logo_icon.setFixedSize(40, 40)

        # 加载logo图片
        import os
        logo_path = os.path.join(os.path.dirname(__file__), 'logo.png')
        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            # 缩放图片以适应标签大小，保持宽高比
            scaled_pixmap = pixmap.scaled(40, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_icon.setPixmap(scaled_pixmap)
        else:
            # 如果图片不存在，显示文字作为备选
            logo_icon.setText("LOGO")
            logo_icon.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #0066cc;
                    background-color: transparent;
                    border: 2px solid #0066cc;
                    border-radius: 20px;
                    padding: 3px;
                }
            """)

        logo_icon.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: none;
                padding: 3px;
            }
        """)
        logo_layout.addWidget(logo_icon)

        parent_layout.addWidget(logo_container)
        
    def setup_animations(self):
        """设置动画效果"""
        # 窗口淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(600)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 容器缩放动画
        self.scale_animation = QPropertyAnimation(self.login_card, b"geometry")
        self.scale_animation.setDuration(500)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)

        # 组合动画
        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.fade_animation)

        # 启动动画
        QTimer.singleShot(100, self.start_entrance_animation)

    def start_entrance_animation(self):
        """启动入场动画"""
        # 设置初始状态
        original_geometry = self.login_card.geometry()

        # 从小到大的缩放效果
        start_geometry = original_geometry.adjusted(40, 40, -40, -40)
        self.login_card.setGeometry(start_geometry)

        self.scale_animation.setStartValue(start_geometry)
        self.scale_animation.setEndValue(original_geometry)

        # 启动动画组
        self.animation_group.start()

    def eventFilter(self, obj, event):
        """事件过滤器 - 处理输入框焦点动画"""
        from PyQt5.QtCore import QEvent

        if obj in [self.username_input, self.password_input]:
            if event.type() == QEvent.FocusIn:
                self.animate_input_focus(obj, True)
            elif event.type() == QEvent.FocusOut:
                self.animate_input_focus(obj, False)

        return super().eventFilter(obj, event)

    def animate_input_focus(self, input_widget, focus_in):
        """输入框焦点动画"""
        if focus_in:
            # 获得焦点时的动画
            input_widget.setStyleSheet(input_widget.styleSheet().replace(
                "border: 1px solid #ddd",
                "border: 1px solid #4169E1"
            ))
        else:
            # 失去焦点时的动画
            input_widget.setStyleSheet(input_widget.styleSheet().replace(
                "border: 1px solid #4169E1",
                "border: 1px solid #ddd"
            ))

    def toggle_password_visibility(self):
        """切换密码可见性"""
        self.password_visible = not self.password_visible

        if self.password_visible:
            self.password_input.setEchoMode(QLineEdit.Normal)
            self.password_toggle_btn.setText("🙈")
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
            self.password_toggle_btn.setText("👁")
    
    def test_database_connection(self):
        """测试数据库连接"""
        self.test_button.setEnabled(False)
        self.test_button.setText("测试中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        # 创建并启动测试线程
        self.test_thread = DatabaseTestThread(self.db_manager)
        self.test_thread.result_ready.connect(self.on_test_result)
        self.test_thread.start()
    
    def on_test_result(self, success, message):
        """处理测试结果"""
        self.test_button.setEnabled(True)
        self.test_button.setText("测试数据库连接")
        self.progress_bar.setVisible(False)
        
        if success:
            self.status_label.setText(message)
            self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; margin: 10px 0; font-size: 14px;")
            self.login_button.setEnabled(True)
        else:
            self.status_label.setText(message)
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; margin: 10px 0; font-size: 14px;")
            self.login_button.setEnabled(False)
    
    def login(self):
        """执行登录"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            self.show_message("请输入用户名和密码", ERROR_COLOR)
            return
        
        # 简单的用户验证（实际项目中应该连接用户数据库）
        if username == "admin" and password == "admin":
            self.show_message("登录成功！", SUCCESS_COLOR)
            
            # 延迟发送登录成功信号
            QTimer.singleShot(1000, lambda: self.login_success.emit(self.db_manager))
        else:
            self.show_message("用户名或密码错误", ERROR_COLOR)
    
    def show_message(self, message, color):
        """显示状态消息"""
        self.status_label.setText(message)

        # 根据消息类型设置不同的背景和样式
        if color == SUCCESS_COLOR:
            bg_color = "rgba(0, 184, 148, 0.15)"
            border_color = "rgba(0, 184, 148, 0.3)"
        elif color == ERROR_COLOR:
            bg_color = "rgba(255, 107, 107, 0.15)"
            border_color = "rgba(255, 107, 107, 0.3)"
        else:
            bg_color = "rgba(108, 92, 231, 0.15)"
            border_color = "rgba(108, 92, 231, 0.3)"

        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 14px;
                font-family: 'Microsoft YaHei', sans-serif;
                font-weight: 500;
                padding: 10px 15px;
                border-radius: 8px;
                background: {bg_color};
                border: 1px solid {border_color};
                min-height: 20px;
            }}
        """)
    
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 创建浅蓝色背景
        from PyQt5.QtGui import QLinearGradient
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(187, 244, 255))  # #bbf4ff 浅蓝色
        gradient.setColorAt(1, QColor(168, 230, 255))  # 稍微深一点的浅蓝色

        painter.fillRect(self.rect(), QBrush(gradient))

        super().paintEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽窗口"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖拽窗口"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec_())

#!/usr/bin/env python3
"""
测试特征方法卡片界面
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from ui.feature_analysis import FeatureAnalysis
from database.db_manager import DatabaseManager

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("特征方法卡片测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建数据库管理器
        self.db_manager = DatabaseManager()
        
        # 创建特征分析界面
        self.feature_analysis = FeatureAnalysis(self.db_manager)
        
        # 设置中央部件
        self.setCentralWidget(self.feature_analysis)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    from ui.styles import get_main_stylesheet
    app.setStyleSheet(get_main_stylesheet())
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

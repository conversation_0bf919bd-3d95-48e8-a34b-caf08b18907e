"""
模型测试对话框
用于加载和测试深度学习模型
"""

import os
import sys
import numpy as np
import pandas as pd
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QTextEdit, QFileDialog, QGroupBox, QFormLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QTabWidget, QWidget,
    QScrollArea, QMessageBox, QProgressBar, QSpinBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

from algorithms import create_deep_learning_classifier
from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)


class ModelTestThread(QThread):
    """模型测试线程"""
    test_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, classifier, test_data):
        super().__init__()
        self.classifier = classifier
        self.test_data = test_data
        
    def run(self):
        """执行模型测试"""
        try:
            # 进行预测
            predictions, probabilities = self.classifier.predict(self.test_data)
            
            results = {
                'predictions': predictions,
                'probabilities': probabilities,
                'test_data': self.test_data
            }
            
            self.test_completed.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class ModelTestDialog(QDialog):
    """模型测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.classifier = None
        self.model_info = {}
        self.test_results = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("深度学习模型测试")
        self.setModal(True)
        self.resize(900, 700)
        
        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
            }}
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: {SECONDARY_BG};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {ACCENT_COLOR};
            }}
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 14px;
            }}
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {SUCCESS_COLOR};
            }}
            QPushButton:disabled {{
                background-color: {TEXT_SECONDARY};
                color: {PRIMARY_BG};
            }}
            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {{
                padding: 8px;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 6px;
                background-color: {SECONDARY_BG};
                color: {TEXT_PRIMARY};
                font-size: 14px;
            }}
            QTableWidget {{
                background-color: {SECONDARY_BG};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 6px;
                gridline-color: {TEXT_SECONDARY};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {TEXT_SECONDARY};
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("深度学习模型测试")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 10px;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 模型加载标签页
        self.create_model_loading_tab()
        
        # 数据输入标签页
        self.create_data_input_tab()
        
        # 测试结果标签页
        self.create_results_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
    def create_model_loading_tab(self):
        """创建模型加载标签页"""
        model_widget = QWidget()
        layout = QVBoxLayout(model_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # 模型加载区域
        load_group = QGroupBox("模型加载")
        load_layout = QVBoxLayout(load_group)
        
        # 模型文件选择
        file_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("选择模型文件...")
        self.model_path_edit.setReadOnly(True)
        file_layout.addWidget(self.model_path_edit)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_model_file)
        file_layout.addWidget(self.browse_btn)
        
        load_layout.addLayout(file_layout)
        
        # 加载按钮
        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_model)
        self.load_model_btn.setEnabled(False)
        load_layout.addWidget(self.load_model_btn)
        
        layout.addWidget(load_group)
        
        # 模型信息显示
        info_group = QGroupBox("模型信息")
        info_layout = QVBoxLayout(info_group)
        
        self.model_info_text = QTextEdit()
        self.model_info_text.setReadOnly(True)
        self.model_info_text.setMaximumHeight(300)
        self.model_info_text.setText("请先加载模型...")
        info_layout.addWidget(self.model_info_text)
        
        layout.addWidget(info_group)
        
        layout.addStretch()
        self.tab_widget.addTab(model_widget, "模型加载")
        
    def create_data_input_tab(self):
        """创建数据输入标签页"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # 数据输入方式选择
        input_group = QGroupBox("数据输入方式")
        input_layout = QVBoxLayout(input_group)
        
        # 文件输入
        file_input_layout = QHBoxLayout()
        self.test_data_path_edit = QLineEdit()
        self.test_data_path_edit.setPlaceholderText("选择测试数据文件 (CSV格式)...")
        self.test_data_path_edit.setReadOnly(True)
        file_input_layout.addWidget(self.test_data_path_edit)
        
        self.browse_data_btn = QPushButton("浏览数据")
        self.browse_data_btn.clicked.connect(self.browse_test_data)
        file_input_layout.addWidget(self.browse_data_btn)
        
        input_layout.addLayout(file_input_layout)
        
        # 手动输入
        manual_label = QLabel("或手动输入特征值 (用逗号分隔):")
        input_layout.addWidget(manual_label)
        
        self.manual_input_edit = QTextEdit()
        self.manual_input_edit.setMaximumHeight(100)
        self.manual_input_edit.setPlaceholderText("例如: 1.2, 3.4, 5.6, 7.8, ...")
        input_layout.addWidget(self.manual_input_edit)
        
        layout.addWidget(input_group)
        
        # 预测按钮
        self.predict_btn = QPushButton("开始预测")
        self.predict_btn.clicked.connect(self.start_prediction)
        self.predict_btn.setEnabled(False)
        layout.addWidget(self.predict_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        self.tab_widget.addTab(data_widget, "数据输入")
        
    def create_results_tab(self):
        """创建测试结果标签页"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        # 预测结果显示
        results_group = QGroupBox("预测结果")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels(["样本", "预测类别", "置信度"])
        
        # 设置表格属性
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        results_layout.addWidget(self.results_table)
        
        layout.addWidget(results_group)
        
        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(150)
        self.stats_text.setText("暂无预测结果...")
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        
        self.tab_widget.addTab(results_widget, "测试结果")
        
    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模型文件", "model/deeplearning_model", "模型文件 (*.pth);;所有文件 (*)"
        )
        
        if file_path:
            self.model_path_edit.setText(file_path)
            self.load_model_btn.setEnabled(True)
            
    def load_model(self):
        """加载模型"""
        try:
            model_path = self.model_path_edit.text()
            if not model_path or not os.path.exists(model_path):
                QMessageBox.warning(self, "警告", "请选择有效的模型文件")
                return
                
            # 创建分类器并加载模型
            self.classifier = create_deep_learning_classifier()
            self.classifier.load_model(model_path)
            
            # 获取模型信息
            self.model_info = self.classifier.get_model_info()
            
            # 显示模型信息
            self.display_model_info()
            
            # 启用预测功能
            self.predict_btn.setEnabled(True)
            
            QMessageBox.information(self, "成功", "模型加载成功!")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载模型失败: {str(e)}")
            
    def display_model_info(self):
        """显示模型信息"""
        if not self.model_info:
            return
            
        info_text = "模型详细信息:\n\n"
        info_text += f"架构类型: {self.model_info.get('architecture', 'Unknown')}\n"
        info_text += f"输入维度: {self.model_info.get('input_size', 'Unknown')}\n"
        info_text += f"隐藏层配置: {self.model_info.get('hidden_units', 'Unknown')}\n"
        info_text += f"输出类别数: {self.model_info.get('n_classes', 'Unknown')}\n"
        info_text += f"Dropout率: {self.model_info.get('dropout_rate', 'Unknown')}\n"
        info_text += f"设备: {self.model_info.get('device', 'Unknown')}\n\n"
        
        # 训练历史信息
        training_history = self.model_info.get('training_history', [])
        if training_history:
            latest_training = training_history[-1]
            info_text += "最近训练信息:\n"
            info_text += f"训练样本数: {latest_training.get('training_samples', 'Unknown')}\n"
            info_text += f"特征数量: {latest_training.get('features', 'Unknown')}\n"
            info_text += f"训练轮数: {latest_training.get('config', {}).get('epochs', 'Unknown')}\n"
            info_text += f"批次大小: {latest_training.get('config', {}).get('batch_size', 'Unknown')}\n"
            info_text += f"学习率: {latest_training.get('config', {}).get('learning_rate', 'Unknown')}\n"
            
        self.model_info_text.setText(info_text)
        
    def browse_test_data(self):
        """浏览测试数据文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择测试数据文件", "", "CSV文件 (*.csv);;所有文件 (*)"
        )
        
        if file_path:
            self.test_data_path_edit.setText(file_path)
            
    def start_prediction(self):
        """开始预测"""
        if self.classifier is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return
            
        try:
            # 获取测试数据
            test_data = self.get_test_data()
            if test_data is None:
                return
                
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.predict_btn.setEnabled(False)
            
            # 启动预测线程
            self.test_thread = ModelTestThread(self.classifier, test_data)
            self.test_thread.test_completed.connect(self.on_test_completed)
            self.test_thread.error_occurred.connect(self.on_test_error)
            self.test_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"预测失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.predict_btn.setEnabled(True)
            
    def get_test_data(self):
        """获取测试数据"""
        # 优先使用文件输入
        file_path = self.test_data_path_edit.text()
        if file_path and os.path.exists(file_path):
            try:
                data = pd.read_csv(file_path)
                # 移除标签列（如果存在）
                if '标签' in data.columns:
                    data = data.drop('标签', axis=1)
                return data.values
            except Exception as e:
                QMessageBox.critical(self, "错误", f"读取数据文件失败: {str(e)}")
                return None
                
        # 使用手动输入
        manual_text = self.manual_input_edit.toPlainText().strip()
        if manual_text:
            try:
                # 解析手动输入的数据
                values = [float(x.strip()) for x in manual_text.split(',') if x.strip()]
                if not values:
                    QMessageBox.warning(self, "警告", "请输入有效的特征值")
                    return None
                    
                # 转换为二维数组（单个样本）
                return np.array([values])
                
            except ValueError:
                QMessageBox.critical(self, "错误", "特征值格式错误，请输入数字并用逗号分隔")
                return None
                
        QMessageBox.warning(self, "警告", "请选择数据文件或手动输入特征值")
        return None
        
    def on_test_completed(self, results):
        """测试完成处理"""
        self.progress_bar.setVisible(False)
        self.predict_btn.setEnabled(True)
        
        self.test_results = results
        self.display_results()
        
        # 切换到结果标签页
        self.tab_widget.setCurrentIndex(2)
        
    def on_test_error(self, error_message):
        """测试错误处理"""
        self.progress_bar.setVisible(False)
        self.predict_btn.setEnabled(True)
        QMessageBox.critical(self, "错误", f"预测失败: {error_message}")
        
    def display_results(self):
        """显示预测结果"""
        if not self.test_results:
            return
            
        predictions = self.test_results['predictions']
        probabilities = self.test_results['probabilities']
        
        # 更新结果表格
        self.results_table.setRowCount(len(predictions))
        
        for i, (pred, prob) in enumerate(zip(predictions, probabilities)):
            # 样本编号
            self.results_table.setItem(i, 0, QTableWidgetItem(f"样本 {i+1}"))
            
            # 预测类别
            self.results_table.setItem(i, 1, QTableWidgetItem(str(pred)))
            
            # 最大置信度
            max_confidence = np.max(prob) if prob is not None else 0.0
            self.results_table.setItem(i, 2, QTableWidgetItem(f"{max_confidence:.4f}"))
            
        # 更新统计信息
        self.update_statistics()
        
    def update_statistics(self):
        """更新统计信息"""
        if not self.test_results:
            return
            
        predictions = self.test_results['predictions']
        probabilities = self.test_results['probabilities']
        
        stats_text = f"预测统计信息:\n\n"
        stats_text += f"总样本数: {len(predictions)}\n"
        
        # 类别分布
        unique_classes, counts = np.unique(predictions, return_counts=True)
        stats_text += f"\n类别分布:\n"
        for cls, count in zip(unique_classes, counts):
            percentage = count / len(predictions) * 100
            stats_text += f"  {cls}: {count} 个样本 ({percentage:.1f}%)\n"
            
        # 置信度统计
        if probabilities is not None:
            max_confidences = np.max(probabilities, axis=1)
            avg_confidence = np.mean(max_confidences)
            min_confidence = np.min(max_confidences)
            max_confidence = np.max(max_confidences)
            
            stats_text += f"\n置信度统计:\n"
            stats_text += f"  平均置信度: {avg_confidence:.4f}\n"
            stats_text += f"  最低置信度: {min_confidence:.4f}\n"
            stats_text += f"  最高置信度: {max_confidence:.4f}\n"
            
        self.stats_text.setText(stats_text)

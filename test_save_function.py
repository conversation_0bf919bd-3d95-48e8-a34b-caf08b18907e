#!/usr/bin/env python3
"""
测试保存功能
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.figure import Figure

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_save_functionality():
    """测试保存功能"""
    print("测试保存功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.feature_analysis import FeatureAnalysis
        from database.db_manager import DatabaseManager
        
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 创建特征分析实例
        feature_analysis = FeatureAnalysis(db_manager)
        
        # 模拟分析结果
        test_results = []
        
        # 创建测试图表1
        fig1 = Figure(figsize=(10, 6))
        ax1 = fig1.add_subplot(111)
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax1.plot(x, y)
        ax1.set_xlabel('时间 (s)')
        ax1.set_ylabel('幅值')
        test_results.append(("时域特征值", fig1))
        
        # 创建测试图表2
        fig2 = Figure(figsize=(10, 6))
        ax2 = fig2.add_subplot(111)
        features = ['均值', '方差', '峰值', '有效值']
        values = [1.2, 2.5, 4.1, 3.2]
        ax2.bar(features, values)
        ax2.set_ylabel('数值')
        test_results.append(("频域特征值", fig2))
        
        # 设置分析结果
        feature_analysis.analysis_results = test_results
        
        print(f"✓ 创建了 {len(test_results)} 个测试结果")
        print(f"✓ analysis_results 已设置，长度: {len(feature_analysis.analysis_results)}")
        
        # 测试保存目录创建
        save_dir = "analysis_results"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            print(f"✓ 创建保存目录: {save_dir}")
        else:
            print(f"✓ 保存目录已存在: {save_dir}")
        
        # 测试文件名生成
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_filename = f"test_analysis_{timestamp}.png"
        test_path = os.path.join(save_dir, test_filename)
        
        # 手动保存测试
        if test_results:
            fig = test_results[0][1]
            fig.savefig(test_path, dpi=300, bbox_inches='tight', pad_inches=0.5)
            print(f"✓ 测试图片保存成功: {test_path}")
            
            # 检查文件是否存在
            if os.path.exists(test_path):
                print(f"✓ 保存的文件确实存在，大小: {os.path.getsize(test_path)} 字节")
            else:
                print("✗ 保存的文件不存在")
        
        print("✓ 保存功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("=" * 50)
    print("保存功能测试")
    print("=" * 50)
    
    test_save_functionality()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()

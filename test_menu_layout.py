#!/usr/bin/env python3
"""
测试菜单栏布局的简单脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from database.db_manager import DatabaseManager
from ui.main_window import MainWindow

def test_menu_layout():
    """测试菜单栏布局"""
    app = QApplication(sys.argv)
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    
    # 创建主窗口
    window = MainWindow(db_manager)
    window.show()
    
    print("菜单栏已集成到主页面顶部区域")
    print("- 菜单栏位于左侧")
    print("- 公司标注位于右侧")
    print("- Logo图标位于最右侧")
    print("- 默认菜单栏已隐藏")
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_menu_layout()

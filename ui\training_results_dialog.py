#!/usr/bin/env python3
"""
训练结果展示对话框
"""

import numpy as np
import pandas as pd
import seaborn as sns
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, 
    QTableWidgetItem, QGroupBox, QScrollArea, QWidget, QPushButton,
    QTabWidget, QTextEdit, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR
)


class TrainingResultsDialog(QDialog):
    """训练结果展示对话框"""
    
    def __init__(self, results, parent=None):
        super().__init__(parent)
        self.results = results
        self.setWindowTitle("训练结果详情")
        self.setModal(True)
        self.resize(1200, 800)
        
        self.init_ui()
        self.apply_styles()
        self.display_results()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("训练结果详情")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                padding: 15px;
                background-color: {SECONDARY_BG};
                border-radius: 10px;
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 性能指标选项卡
        self.metrics_tab = QWidget()
        self.tab_widget.addTab(self.metrics_tab, "性能指标")
        
        # 混淆矩阵选项卡
        self.confusion_tab = QWidget()
        self.tab_widget.addTab(self.confusion_tab, "混淆矩阵")
        
        # 特征重要性选项卡
        self.importance_tab = QWidget()
        self.tab_widget.addTab(self.importance_tab, "特征重要性")
        
        # 详细报告选项卡
        self.report_tab = QWidget()
        self.tab_widget.addTab(self.report_tab, "详细报告")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {SUCCESS_COLOR};
            }}
        """)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def display_results(self):
        """显示结果"""
        self.create_metrics_tab()
        self.create_confusion_tab()
        self.create_importance_tab()
        self.create_report_tab()
        
    def create_metrics_tab(self):
        """创建性能指标选项卡"""
        layout = QVBoxLayout(self.metrics_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 获取评估结果
        eval_result = self.results['evaluation_result']
        training_result = self.results['training_result']
        
        # 性能指标表格
        metrics_group = QGroupBox("模型性能指标")
        metrics_layout = QVBoxLayout(metrics_group)
        
        metrics_table = QTableWidget(4, 2)
        metrics_table.setHorizontalHeaderLabels(["指标", "数值"])
        metrics_table.horizontalHeader().setStretchLastSection(True)
        
        metrics_data = [
            ("准确率", f"{eval_result['accuracy']:.4f}"),
            ("精确率", f"{eval_result['precision']:.4f}"),
            ("召回率", f"{eval_result['recall']:.4f}"),
            ("F1分数", f"{eval_result['f1_score']:.4f}")
        ]
        
        for i, (metric, value) in enumerate(metrics_data):
            metrics_table.setItem(i, 0, QTableWidgetItem(metric))
            metrics_table.setItem(i, 1, QTableWidgetItem(value))
        
        metrics_table.setMaximumHeight(220)
        metrics_layout.addWidget(metrics_table)
        layout.addWidget(metrics_group)
        
        # 训练结果
        training_group = QGroupBox("训练结果")
        training_layout = QVBoxLayout(training_group)
        
        training_label = QLabel(f"算法: {training_result['algorithm']}\n"
                               f"最佳分数: {training_result['best_score']:.4f}\n"
                               f"训练样本数: {training_result['training_samples']}\n"
                               f"特征数: {training_result['features']}")
        training_layout.addWidget(training_label)
        layout.addWidget(training_group)
        
    def create_confusion_tab(self):
        """创建混淆矩阵选项卡"""
        layout = QVBoxLayout(self.confusion_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        eval_result = self.results['evaluation_result']
        
        # 创建matplotlib图形
        fig = Figure(figsize=(10, 8), facecolor=PRIMARY_BG)
        canvas = FigureCanvas(fig)
        
        ax = fig.add_subplot(111)
        
        # 获取类别标签
        labels = ['正常', '内圈故障', '外圈故障', '滚动体故障']
        
        # 绘制混淆矩阵热图
        sns.heatmap(eval_result['confusion_matrix'], annot=True, fmt='d', cmap='Blues',
                   xticklabels=labels, yticklabels=labels, ax=ax,
                   cbar_kws={'shrink': 0.8})
        
        # 设置标题位置在下方
        ax.set_xlabel('混淆矩阵', fontsize=20, color=TEXT_PRIMARY, fontweight='bold')
        ax.set_ylabel('真实标签', fontsize=16, color=TEXT_PRIMARY)
        ax.tick_params(axis='x', labelsize=14, colors=TEXT_PRIMARY)
        ax.tick_params(axis='y', labelsize=14, colors=TEXT_PRIMARY)
        
        # 设置图表样式
        ax.set_facecolor(SECONDARY_BG)
        fig.patch.set_facecolor(PRIMARY_BG)
        
        fig.tight_layout()
        
        layout.addWidget(canvas)
        
    def create_importance_tab(self):
        """创建特征重要性选项卡"""
        layout = QVBoxLayout(self.importance_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        if self.results['feature_importance'] is None:
            no_importance_label = QLabel("该算法不支持特征重要性分析")
            no_importance_label.setAlignment(Qt.AlignCenter)
            no_importance_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 18px;
                    color: {TEXT_SECONDARY};
                    padding: 50px;
                }}
            """)
            layout.addWidget(no_importance_label)
            return
        
        feature_importance = self.results['feature_importance']
        
        # 创建matplotlib图形
        fig = Figure(figsize=(12, 8), facecolor=PRIMARY_BG)
        canvas = FigureCanvas(fig)
        
        ax = fig.add_subplot(111)
        
        # 获取特征名称
        feature_names = [f'特征_{i+1}' for i in range(len(feature_importance))]
        
        # 排序特征重要性
        indices = np.argsort(feature_importance)[::-1][:10]  # 显示前10个重要特征
        
        # 绘制条形图
        bars = ax.bar(range(len(indices)), feature_importance[indices], 
                     color=ACCENT_COLOR, alpha=0.8, edgecolor='white', linewidth=2)
        
        # 设置标题位置在下方
        ax.set_xlabel('特征重要性 (前10名)', fontsize=20, color=TEXT_PRIMARY, fontweight='bold')
        ax.set_ylabel('重要性', fontsize=16, color=TEXT_PRIMARY)
        ax.set_xticks(range(len(indices)))
        ax.set_xticklabels([feature_names[i] for i in indices], rotation=45, ha='right')
        ax.tick_params(axis='x', labelsize=14, colors=TEXT_PRIMARY)
        ax.tick_params(axis='y', labelsize=14, colors=TEXT_PRIMARY)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom', 
                   fontsize=12, color=TEXT_PRIMARY, fontweight='bold')
        
        # 设置图表样式
        ax.set_facecolor(SECONDARY_BG)
        ax.grid(True, alpha=0.3, color=TEXT_SECONDARY)
        fig.patch.set_facecolor(PRIMARY_BG)
        
        fig.tight_layout()
        
        layout.addWidget(canvas)
        
    def create_report_tab(self):
        """创建详细报告选项卡"""
        layout = QVBoxLayout(self.report_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        eval_result = self.results['evaluation_result']
        
        # 分类报告
        if 'classification_report' in eval_result:
            report_text = QTextEdit()
            report_text.setReadOnly(True)
            
            # 格式化分类报告
            report_dict = eval_result['classification_report']
            report_str = "分类报告:\n\n"
            
            for class_name, metrics in report_dict.items():
                if isinstance(metrics, dict):
                    report_str += f"{class_name}:\n"
                    report_str += f"  精确率: {metrics.get('precision', 0):.4f}\n"
                    report_str += f"  召回率: {metrics.get('recall', 0):.4f}\n"
                    report_str += f"  F1分数: {metrics.get('f1-score', 0):.4f}\n"
                    report_str += f"  支持数: {metrics.get('support', 0)}\n\n"
            
            report_text.setPlainText(report_str)
            layout.addWidget(report_text)
        
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
                color: {TEXT_PRIMARY};
            }}
            QTabWidget::pane {{
                border: 2px solid {SECONDARY_BG};
                border-radius: 8px;
                background-color: {PRIMARY_BG};
            }}
            QTabBar::tab {{
                background-color: {SECONDARY_BG};
                color: {TEXT_PRIMARY};
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {ACCENT_COLOR};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {INFO_COLOR};
                color: white;
            }}
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                border: 2px solid {SECONDARY_BG};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
            }}
            QTableWidget {{
                background-color: {SECONDARY_BG};
                border: 1px solid {TEXT_SECONDARY};
                border-radius: 8px;
                font-size: 16px;
                color: {TEXT_PRIMARY};
            }}
            QTableWidget::item {{
                padding: 10px;
                border-bottom: 1px solid {TEXT_SECONDARY};
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 16px;
            }}
            QLabel {{
                font-size: 18px; 
                color: {TEXT_PRIMARY};
                padding: 15px;
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                line-height: 1.5;
            }}
            QTextEdit {{
                background-color: {SECONDARY_BG};
                border: 1px solid {TEXT_SECONDARY};
                border-radius: 8px;
                font-size: 14px;
                color: {TEXT_PRIMARY};
                padding: 10px;
            }}
        """)
